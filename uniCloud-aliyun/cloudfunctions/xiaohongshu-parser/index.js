'use strict';

/**
 * 小红书内容解析器
 * 支持小红书视频和图文内容的解析，包括去水印处理
 */

// 全局日志收集器
let debugLogs = [];

function addDebugLog(message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}`;
  console.log(logEntry);
  debugLogs.push(logEntry);
}

exports.main = async (event, context) => {
  // 重置日志
  debugLogs = [];
  addDebugLog('收到小红书解析请求: ' + JSON.stringify(event));

  const { link, forceRemoveWatermark = false, debug = false, saveHtml = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为小红书链接
    if (!isXiaohongshuLink(link)) {
      return {
        success: false,
        message: '仅支持小红书链接'
      };
    }

    addDebugLog('开始解析小红书链接: ' + link);
    addDebugLog('调试模式: ' + debug);
    addDebugLog('去水印模式: ' + forceRemoveWatermark);

    // 解析小红书内容
    const result = await parseXiaohongshuContent(link, saveHtml);

    addDebugLog('解析完成，返回结果: ' + JSON.stringify(result));

    return {
      success: true,
      data: result,
      debugLogs: debugLogs // 总是返回调试日志，方便分析
    };

  } catch (error) {
    addDebugLog('解析失败: ' + error.message);
    addDebugLog('错误堆栈: ' + error.stack);

    return {
      success: false,
      message: error.message || '解析失败',
      debugLogs: debugLogs, // 总是返回调试日志
      error: debug ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : undefined
    };
  }
};

// 检测是否为小红书链接
function isXiaohongshuLink(url) {
  const patterns = [
    /xiaohongshu\.com/i,
    /xhslink\.com/i,
    /xhs\.link/i
  ];
  return patterns.some(pattern => pattern.test(url));
}

// 分析小红书内容类型（视频 vs 图文）
function analyzeContentType(html) {
  try {
    addDebugLog('开始分析内容类型...');

    // 查找可能的类型字段
    const typePatterns = [
      /"type":\s*"([^"]+)"/g,
      /"noteType":\s*"([^"]+)"/g,
      /"note_type":\s*"([^"]+)"/g,
      /"contentType":\s*"([^"]+)"/g,
      /"content_type":\s*"([^"]+)"/g,
      /"mediaType":\s*"([^"]+)"/g,
      /"media_type":\s*"([^"]+)"/g
    ];

    const foundTypes = new Set();

    typePatterns.forEach((pattern, index) => {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        foundTypes.add(`${pattern.source.split(':')[0].replace(/[/"]/g, '')}: "${match[1]}"`);
      }
    });

    addDebugLog('找到的类型字段:');
    if (foundTypes.size > 0) {
      foundTypes.forEach(type => addDebugLog('- ' + type));
    } else {
      addDebugLog('- 未找到明确的类型字段');
    }

    // 查找视频相关字段
    addDebugLog('查找视频相关字段:');
    const hasVideo = html.includes('"video"') || html.includes('"stream"') || html.includes('.mp4');
    const hasVideoField = /"video":\s*{/.test(html);
    const hasStreamField = /"stream":\s*{/.test(html);
    const hasMp4Url = /\.mp4/.test(html);

    addDebugLog('- 包含"video"字段: ' + hasVideoField);
    addDebugLog('- 包含"stream"字段: ' + hasStreamField);
    addDebugLog('- 包含.mp4链接: ' + hasMp4Url);
    addDebugLog('- 总体包含视频: ' + hasVideo);

    // 查找图片相关字段
    addDebugLog('查找图片相关字段:');
    const hasImages = html.includes('"images"') || html.includes('"image_list"');
    const hasImageField = /"images":\s*\[/.test(html);
    const hasImageListField = /"image_list":\s*\[/.test(html);
    const hasJpgUrl = /\.(jpg|jpeg|png|webp)/.test(html);

    addDebugLog('- 包含"images"字段: ' + hasImageField);
    addDebugLog('- 包含"image_list"字段: ' + hasImageListField);
    addDebugLog('- 包含图片链接: ' + hasJpgUrl);
    addDebugLog('- 总体包含图片: ' + hasImages);

    // 尝试从JSON数据中提取更详细的信息
    addDebugLog('查找JSON数据中的详细类型信息:');

    // 查找包含type的JSON片段
    const typeJsonMatches = html.match(/{[^}]*"type":\s*"[^"]+"[^}]*}/g);
    if (typeJsonMatches) {
      addDebugLog('找到包含type的JSON片段:');
      typeJsonMatches.slice(0, 5).forEach((match, index) => {
        addDebugLog(`片段${index + 1}: ${match}`);
      });
    }

    // 查找包含noteType的JSON片段
    const noteTypeMatches = html.match(/{[^}]*"noteType":\s*"[^"]+"[^}]*}/g);
    if (noteTypeMatches) {
      addDebugLog('找到包含noteType的JSON片段:');
      noteTypeMatches.slice(0, 3).forEach((match, index) => {
        addDebugLog(`片段${index + 1}: ${match}`);
      });
    }

    addDebugLog('=== 内容类型分析结束 ===');

  } catch (error) {
    addDebugLog('内容类型分析失败: ' + error.message);
  }
}



// 从页面内容中提取图片和视频URL（支持Live Photo）
async function extractMediaUrls(html) {
  try {
    addDebugLog('开始提取媒体URL（图片和视频）...');
    let imageUrls = [];
    let videoUrls = [];

    // 先检查页面中是否包含图片相关的关键词
    addDebugLog('检查页面中的图片相关关键词...');
    addDebugLog('- 包含"image_list": ' + html.includes('"image_list"'));
    addDebugLog('- 包含"images": ' + html.includes('"images"'));
    addDebugLog('- 包含"imageView": ' + html.includes('imageView'));
    addDebugLog('- 包含"xhscdn": ' + html.includes('xhscdn'));
    addDebugLog('- 包含".jpg": ' + html.includes('.jpg'));
    addDebugLog('- 包含".webp": ' + html.includes('.webp'));

    // 方法1：查找所有可能的图片和视频数据结构
    addDebugLog('=== 开始查找所有可能的数据结构 ===');

    // 查找所有包含图片或视频的JSON结构
    const dataPatterns = [
      /"image_list":\s*(\[[^\]]*\])/g,
      /"images":\s*(\[[^\]]*\])/g,
      /"imageList":\s*(\[[^\]]*\])/g,
      /"pics":\s*(\[[^\]]*\])/g,
      /"media":\s*(\[[^\]]*\])/g,
      /"attachments":\s*(\[[^\]]*\])/g
    ];

    let foundAnyData = false;

    for (const pattern of dataPatterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        try {
          foundAnyData = true;
          const dataListStr = match[1];
          addDebugLog('找到数据列表JSON (' + pattern.source + '): ' + dataListStr.substring(0, 200) + '...');

          // 尝试解析JSON
          const dataList = JSON.parse(dataListStr);
          if (Array.isArray(dataList)) {
            for (let i = 0; i < dataList.length; i++) {
              const dataItem = dataList[i];
              if (typeof dataItem === 'object' && dataItem !== null) {
                addDebugLog('=== 处理第' + (i + 1) + '个数据项 ===');
                addDebugLog('数据项顶级字段: ' + Object.keys(dataItem).join(', '));

                // 提取静态图片URL
                const imageUrl = dataItem.url_size_large ||
                               dataItem.url_default ||
                               dataItem.url ||
                               dataItem.src ||
                               dataItem.image_url;

                if (imageUrl && typeof imageUrl === 'string') {
                  // 解码URL中的Unicode字符
                  const cleanImageUrl = imageUrl.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
                  imageUrls.push(cleanImageUrl);
                  addDebugLog('提取到图片URL: ' + cleanImageUrl);
                }

                // 查找所有可能的视频相关字段
                addDebugLog('=== 查找视频相关字段 ===');

                // 检查所有可能包含视频的字段
                const possibleVideoFields = [
                  'live_photo', 'video', 'stream', 'media', 'live', 'motion',
                  'dynamic', 'animated', 'mp4', 'h264', 'videos', 'clips'
                ];

                let foundVideoField = false;
                for (const field of possibleVideoFields) {
                  if (dataItem[field]) {
                    foundVideoField = true;
                    addDebugLog('发现视频字段 "' + field + '": ' + JSON.stringify(dataItem[field], null, 2).substring(0, 300));

                    // 尝试从这个字段中提取视频URL
                    const extractedVideos = extractVideoFromField(dataItem[field], field);
                    if (extractedVideos.length > 0) {
                      videoUrls.push(...extractedVideos);
                      addDebugLog('从字段 "' + field + '" 提取到 ' + extractedVideos.length + ' 个视频URL');
                    }
                  }
                }

                if (!foundVideoField) {
                  addDebugLog('未发现任何视频相关字段');
                }

                // 特别检查是否有直接的视频URL字段
                const directVideoFields = ['video_url', 'mp4_url', 'stream_url', 'src'];
                for (const field of directVideoFields) {
                  if (dataItem[field] && typeof dataItem[field] === 'string' && dataItem[field].includes('.mp4')) {
                    const cleanVideoUrl = dataItem[field].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
                    videoUrls.push(cleanVideoUrl);
                    addDebugLog('从直接字段 "' + field + '" 提取到视频URL: ' + cleanVideoUrl);
                  }
                }
              }
            }
          }
        } catch (parseError) {
          addDebugLog('解析图片列表JSON失败: ' + parseError.message);
        }
      }
    }

    // 方法2：直接匹配图片URL
    if (imageUrls.length === 0) {
      addDebugLog('JSON方法未找到图片，尝试直接匹配...');

      // 更全面的图片URL匹配模式
      const directImagePatterns = [
        // 小红书CDN图片
        /https?:\/\/[^"'\s]*sns-img[^"'\s]*\.(jpg|jpeg|png|webp)[^"'\s]*/gi,
        /https?:\/\/[^"'\s]*xhscdn[^"'\s]*\.(jpg|jpeg|png|webp)[^"'\s]*/gi,
        // 带imageView参数的图片
        /https?:\/\/[^"'\s]*imageView[^"'\s]*/gi,
        // 通用图片URL
        /https?:\/\/[^"'\s]*\.(jpg|jpeg|png|webp)\?[^"'\s]*/gi,
        /https?:\/\/[^"'\s]*\.(jpg|jpeg|png|webp)[^"'\s]*/gi
      ];

      for (const pattern of directImagePatterns) {
        const matches = html.match(pattern);
        if (matches) {
          addDebugLog('直接匹配找到图片: ' + matches.length + ' 张，模式: ' + pattern.source);
          for (const match of matches) {
            // 更严格的过滤条件，避免其他作品的图片和界面元素
            if (!match.includes('avatar') &&
                !match.includes('icon') &&
                !match.includes('logo') &&
                !match.includes('default') &&
                !match.includes('placeholder') &&
                !match.includes('thumb') &&
                !match.includes('cover') &&
                !match.includes('recommend') &&
                !match.includes('related') &&
                !match.includes('similar') &&
                // 过滤掉明显的缩略图（通常尺寸较小）
                !match.includes('w_100') &&
                !match.includes('w_200') &&
                !match.includes('h_100') &&
                !match.includes('h_200') &&
                // 过滤掉可能的推荐内容图片
                !match.includes('feed') &&
                !match.includes('list') &&
                // 过滤掉小红书界面静态资源
                !match.includes('picasso-static') &&
                !match.includes('fe-platform') &&
                !match.includes('static.xiaohongshu.com')) {
              const cleanUrl = match.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
              if (!imageUrls.includes(cleanUrl)) {
                imageUrls.push(cleanUrl);
                addDebugLog('添加直接匹配的图片: ' + cleanUrl);
              }
            }
          }
        }
      }
    }

    // 方法3：如果还是没找到，尝试查找JSON中的URL字段
    if (imageUrls.length === 0) {
      addDebugLog('直接匹配也未找到图片，尝试查找JSON中的URL字段...');

      // 查找所有可能包含图片URL的JSON字段
      const urlPatterns = [
        /"url":\s*"([^"]*(?:jpg|jpeg|png|webp)[^"]*)"/gi,
        /"src":\s*"([^"]*(?:jpg|jpeg|png|webp)[^"]*)"/gi,
        /"image":\s*"([^"]*(?:jpg|jpeg|png|webp)[^"]*)"/gi
      ];

      for (const pattern of urlPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const url = match[1];
          if (url && url.includes('xhscdn') &&
              !url.includes('avatar') &&
              !url.includes('thumb') &&
              !url.includes('cover') &&
              !url.includes('recommend') &&
              !url.includes('w_100') &&
              !url.includes('w_200') &&
              !url.includes('picasso-static') &&
              !url.includes('fe-platform') &&
              !url.includes('static.xiaohongshu.com')) {
            const cleanUrl = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            if (!imageUrls.includes(cleanUrl)) {
              imageUrls.push(cleanUrl);
              addDebugLog('从JSON URL字段找到图片: ' + cleanUrl);
            }
          }
        }
      }
    }

    // 方法3：直接匹配视频URL（如果JSON方法没找到）
    if (videoUrls.length === 0) {
      addDebugLog('JSON方法未找到视频，尝试直接匹配...');

      const directVideoPatterns = [
        /https?:\/\/[^"'\s]*sns-video[^"'\s]*\.mp4[^"'\s]*/gi,
        /https?:\/\/[^"'\s]*\.mp4[^"'\s]*/gi
      ];

      for (const pattern of directVideoPatterns) {
        const matches = html.match(pattern);
        if (matches) {
          addDebugLog('直接匹配找到视频: ' + matches.length + ' 个');
          for (const match of matches) {
            const cleanUrl = match.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            if (!videoUrls.includes(cleanUrl)) {
              videoUrls.push(cleanUrl);
              addDebugLog('添加直接匹配的视频: ' + cleanUrl);
            }
          }
        }
      }
    }

    // 方法4：尝试查找其他可能的Live Photo结构
    if (videoUrls.length === 0) {
      addDebugLog('尝试查找其他可能的Live Photo结构...');

      // 查找可能的Live Photo相关字段
      const livePhotoPatterns = [
        /"live_photo"[^}]*"url":\s*"([^"]+\.mp4[^"]*)"/gi,
        /"live"[^}]*"video"[^}]*"url":\s*"([^"]+\.mp4[^"]*)"/gi,
        /"motion"[^}]*"url":\s*"([^"]+\.mp4[^"]*)"/gi,
        /"stream"[^}]*"url":\s*"([^"]+\.mp4[^"]*)"/gi,
        /"video_url":\s*"([^"]+\.mp4[^"]*)"/gi
      ];

      for (const pattern of livePhotoPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const videoUrl = match[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
          if (!videoUrls.includes(videoUrl)) {
            videoUrls.push(videoUrl);
            addDebugLog('通过模式匹配找到Live Photo视频: ' + videoUrl);
          }
        }
      }
    }

    // 方法4：最后的尝试 - 查找所有可能的小红书图片URL
    if (imageUrls.length === 0) {
      addDebugLog('所有方法都未找到图片，进行最后的全面搜索...');

      // 查找所有包含小红书CDN域名的URL
      const allXhsUrls = html.match(/https?:\/\/[^"'\s]*xhscdn[^"'\s]*/gi);
      if (allXhsUrls) {
        addDebugLog('找到所有xhscdn URL: ' + allXhsUrls.length + ' 个');
        for (const url of allXhsUrls) {
          // 只要是图片格式或包含imageView的都加入，但要严格过滤
          if ((url.includes('imageView') ||
               url.includes('.jpg') ||
               url.includes('.jpeg') ||
               url.includes('.png') ||
               url.includes('.webp')) &&
              // 严格过滤条件
              !url.includes('avatar') &&
              !url.includes('thumb') &&
              !url.includes('cover') &&
              !url.includes('recommend') &&
              !url.includes('related') &&
              !url.includes('similar') &&
              !url.includes('feed') &&
              !url.includes('list') &&
              !url.includes('w_100') &&
              !url.includes('w_200') &&
              !url.includes('h_100') &&
              !url.includes('h_200') &&
              // 过滤掉小红书界面静态资源
              !url.includes('picasso-static') &&
              !url.includes('fe-platform') &&
              !url.includes('static.xiaohongshu.com')) {
            const cleanUrl = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            if (!imageUrls.includes(cleanUrl)) {
              imageUrls.push(cleanUrl);
              addDebugLog('最后搜索找到图片: ' + cleanUrl);
            }
          }
        }
      }
    }

    // 去重但不限制数量
    imageUrls = [...new Set(imageUrls)];
    videoUrls = [...new Set(videoUrls)];

    // 进一步过滤：基于图片URL的特征进行智能过滤
    if (imageUrls.length > 0) {
      addDebugLog('开始智能过滤图片...');
      const filteredImageUrls = [];

      for (const url of imageUrls) {
        let shouldInclude = true;

        // 检查是否是明显的推荐/相关内容图片或界面元素
        if (url.includes('spectrum') ||     // 小红书推荐算法相关
            url.includes('discovery') ||    // 发现页面
            url.includes('explore') ||      // 探索页面
            url.includes('picasso-static') || // 界面静态资源
            url.includes('fe-platform') ||  // 前端平台资源
            url.includes('static.xiaohongshu.com') || // 静态资源域名
            url.match(/w_\d{1,3}/) ||      // 小尺寸缩略图 (w_100, w_200等)
            url.match(/h_\d{1,3}/)) {      // 小尺寸缩略图 (h_100, h_200等)
          shouldInclude = false;
          addDebugLog('过滤掉非内容图片: ' + url);
        }

        if (shouldInclude) {
          filteredImageUrls.push(url);
        }
      }

      imageUrls = filteredImageUrls;
      addDebugLog('智能过滤后图片数量: ' + imageUrls.length);
    }

    addDebugLog('最终提取到图片数量: ' + imageUrls.length);
    addDebugLog('最终提取到视频数量: ' + videoUrls.length);

    // 如果找到了图片，输出前几个用于调试
    if (imageUrls.length > 0) {
      addDebugLog('前5个图片URL: ' + JSON.stringify(imageUrls.slice(0, 5)));
    }

    return {
      imageUrls: imageUrls,
      videoUrls: videoUrls
    };

  } catch (error) {
    addDebugLog('提取媒体URL失败: ' + error.message);
    return {
      imageUrls: [],
      videoUrls: []
    };
  }
}

// 小红书视频去水印处理
function removeWatermarkFromUrl(videoUrl) {
  try {
    if (!videoUrl || typeof videoUrl !== 'string') {
      return videoUrl;
    }

    // 小红书去水印的常见方法：
    // 1. 移除URL中的水印参数
    let cleanUrl = videoUrl;

    // 移除常见的水印参数
    const watermarkParams = [
      'watermark=1',
      'watermark=true',
      'wm=1',
      'logo=1'
    ];

    watermarkParams.forEach(param => {
      cleanUrl = cleanUrl.replace(new RegExp('[?&]' + param, 'g'), '');
    });

    // 2. 替换域名（如果有特定的无水印域名）
    // 这里可以根据实际情况添加域名替换逻辑

    // 3. 清理多余的参数分隔符
    cleanUrl = cleanUrl.replace(/[?&]$/, ''); // 移除末尾的?或&
    cleanUrl = cleanUrl.replace(/\?&/, '?'); // 将?&替换为?

    return cleanUrl;

  } catch (error) {
    console.log('去水印处理失败:', error.message);
    return videoUrl; // 如果处理失败，返回原URL
  }
}

// 保存HTML源码到云存储
async function saveHtmlToStorage(htmlContent, fileName) {
  try {
    const cloudStorage = uniCloud.storage();

    // 将HTML内容转换为Buffer
    const htmlBuffer = Buffer.from(htmlContent, 'utf8');

    // 上传到云存储
    const uploadResult = await cloudStorage.uploadFile({
      cloudPath: `xiaohongshu-html/${fileName}`,
      fileContent: htmlBuffer
    });

    console.log('HTML文件上传成功:', uploadResult.fileID);

    // 获取临时下载链接（有效期1小时）
    const tempUrlResult = await cloudStorage.getTempFileURL({
      fileList: [uploadResult.fileID],
      maxAge: 3600 // 1小时
    });

    if (tempUrlResult.fileList && tempUrlResult.fileList[0]) {
      const downloadUrl = tempUrlResult.fileList[0].tempFileURL;
      console.log('HTML文件下载链接:', downloadUrl);
      return {
        fileId: uploadResult.fileID,
        downloadUrl: downloadUrl,
        fileName: fileName
      };
    }

    return {
      fileId: uploadResult.fileID,
      fileName: fileName
    };

  } catch (error) {
    console.error('保存HTML文件失败:', error);
    throw error;
  }
}

// 解析小红书内容（重新设计）
async function parseXiaohongshuContent(shareUrl, saveHtml = false) {
  addDebugLog('开始解析小红书链接: ' + shareUrl);
  addDebugLog('保存HTML源码: ' + saveHtml);

  try {
    // 第一步：获取真实URL并提取note_id
    const realUrl = await getRealUrl(shareUrl);
    addDebugLog('真实URL: ' + realUrl);

    // 提取note_id
    const noteId = extractNoteId(realUrl);
    addDebugLog('提取到的note_id: ' + noteId);

    // 从URL参数中判断内容类型
    const urlContentType = extractContentType(realUrl);
    addDebugLog('从URL提取到的内容类型: ' + urlContentType);

    if (!noteId) {
      throw new Error('无法从URL中提取note_id');
    }

    // 第二步：从页面中直接提取视频URL
    addDebugLog('从页面中直接提取视频URL');

    // 获取页面内容
    const pageContent = await getPageContent(realUrl);
    addDebugLog('成功获取页面内容，长度: ' + pageContent.length);

    // 简化调试信息
    addDebugLog('HTML内容长度: ' + pageContent.length);

    // 使用URL参数判断内容类型
    const contentType = urlContentType;
    addDebugLog('使用URL参数判断的内容类型: ' + contentType);

    // 如果需要保存HTML源码
    let htmlFileInfo = null;
    if (saveHtml) {
      addDebugLog('保存HTML源码到云存储...');
      try {
        const htmlFileName = `xiaohongshu_${noteId}_${Date.now()}.html`;
        htmlFileInfo = await saveHtmlToStorage(pageContent, htmlFileName);
        addDebugLog('HTML源码已保存: ' + htmlFileName);
        addDebugLog('下载链接: ' + (htmlFileInfo.downloadUrl || '无'));
      } catch (saveError) {
        addDebugLog('保存HTML源码失败: ' + saveError.message);
      }
    }

    // 提取基本信息
    const basicInfo = await extractBasicInfo(pageContent, noteId);
    addDebugLog('提取到基本信息 - 标题: ' + basicInfo.title + ', 作者: ' + basicInfo.author + ', 正文长度: ' + (basicInfo.content ? basicInfo.content.length : 0));

    // 根据内容类型提取相应的媒体内容
    let imageUrls = [];
    let videoUrls = [];
    let videoUrl = null;

    if (contentType === 'image') {
      addDebugLog('内容类型为图文，提取图片和Live Photo...');
      const mediaResult = await extractMediaUrls(pageContent);
      imageUrls = mediaResult.imageUrls;
      videoUrls = mediaResult.videoUrls; // Live Photo视频

      addDebugLog('提取到图片数量: ' + imageUrls.length);
      addDebugLog('提取到Live Photo视频数量: ' + videoUrls.length);

      if (imageUrls.length > 0) {
        addDebugLog('图片URL列表: ' + JSON.stringify(imageUrls.slice(0, 3))); // 只显示前3个
      }
      if (videoUrls.length > 0) {
        addDebugLog('Live Photo视频URL列表: ' + JSON.stringify(videoUrls.slice(0, 3))); // 只显示前3个
      }

      // 如果图文内容没找到Live Photo视频，也尝试传统的JSON提取方法
      if (videoUrls.length === 0) {
        addDebugLog('图文内容未找到Live Photo视频，尝试传统JSON提取方法...');

        const videoPatterns = [
          // 标准的小红书视频URL模式 - 使用全局匹配
          /"video"[^}]*"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/g,
          /"video"[^}]*"stream"[^}]*"h264"[^}]*"backup_urls":\s*\[\s*"([^"]+)"/g,
          /"video"[^}]*"media"[^}]*"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/g,
          /"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/g,
          /"h264"[^}]*"master_url":\s*"([^"]+)"/g,
          /"master_url":\s*"([^"]+\.mp4[^"]*)"/g,
          /"backup_urls":\s*\[\s*"([^"]+\.mp4[^"]*)"/g,
          // 更宽泛的模式
          /"master_url":\s*"([^"]+)"/g,
          /"backup_urls":\s*\[\s*"([^"]+)"/g,
          // 直接查找sns-video域名的URL
          /"(https?:[^"]*sns-video[^"]*\.mp4[^"]*)"/g,
          /"(https?:[^"]*sns-video[^"]*)"/g
        ];

        for (const pattern of videoPatterns) {
          let match;
          // 使用全局匹配获取所有结果
          while ((match = pattern.exec(pageContent)) !== null) {
            let url = match[1];
            // 解码Unicode字符
            url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            // 检查是否为视频URL（不是图片URL）
            if (url.includes('.mp4') || (url.includes('sns-') && !url.includes('imageView'))) {
              // 避免重复添加
              if (!videoUrls.includes(url)) {
                videoUrls.push(url);
                addDebugLog('从传统JSON方法找到Live Photo视频URL: ' + url);
              }
            } else {
              addDebugLog('跳过非视频URL: ' + url);
            }
          }
          // 重置正则表达式的lastIndex
          pattern.lastIndex = 0;
        }

        addDebugLog('传统JSON方法提取到视频数量: ' + videoUrls.length);

        // 如果还是没有找到足够的视频，尝试直接提取所有.mp4 URL
        if (videoUrls.length < imageUrls.length) {
          addDebugLog('视频数量少于图片数量，尝试直接提取所有.mp4 URL...');

          // 直接匹配所有包含.mp4的URL
          const mp4Pattern = /"(https?:[^"]*\.mp4[^"]*)"/g;
          let mp4Match;
          while ((mp4Match = mp4Pattern.exec(pageContent)) !== null) {
            let url = mp4Match[1];
            // 解码Unicode字符
            url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            // 过滤掉明显不是Live Photo的URL
            if (url.includes('sns-video') && !url.includes('imageView') && !videoUrls.includes(url)) {
              videoUrls.push(url);
              addDebugLog('直接提取到.mp4视频URL: ' + url);
            }
          }
          mp4Pattern.lastIndex = 0;

          addDebugLog('直接提取后视频总数量: ' + videoUrls.length);
        }
      }
    } else if (contentType === 'video') {
      addDebugLog('内容类型为视频，提取视频URL...');
      // 对于视频内容，优先使用传统的视频提取方法
      // 这里可以先尝试新的方法，如果没找到再用旧方法
      const mediaResult = await extractMediaUrls(pageContent);
      videoUrls = mediaResult.videoUrls;
      imageUrls = mediaResult.imageUrls; // 可能的封面图

      if (videoUrls.length > 0) {
        videoUrl = videoUrls[0];
        addDebugLog('从新方法找到视频URL: ' + videoUrl);
      }

      addDebugLog('提取到视频数量: ' + videoUrls.length);
      addDebugLog('提取到封面图数量: ' + imageUrls.length);
    } else {
      addDebugLog('内容类型未知，同时尝试提取图片和视频...');
      const mediaResult = await extractMediaUrls(pageContent);
      imageUrls = mediaResult.imageUrls;
      videoUrls = mediaResult.videoUrls;

      if (videoUrls.length > 0) {
        videoUrl = videoUrls[0];
      }

      addDebugLog('提取到图片数量: ' + imageUrls.length);
      addDebugLog('提取到视频数量: ' + videoUrls.length);
    }

    // 如果是视频内容但没找到视频URL，尝试传统的JSON提取方法
    if (contentType === 'video' && !videoUrl) {
      addDebugLog('视频内容但未找到视频URL，尝试传统JSON提取方法...');

      // 先尝试查找真正的视频URL
      const videoPatterns = [
        // 标准的小红书视频URL模式
        /"video"[^}]*"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/,
        /"video"[^}]*"stream"[^}]*"h264"[^}]*"backup_urls":\s*\[\s*"([^"]+)"/,
        /"video"[^}]*"media"[^}]*"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/,
        /"stream"[^}]*"h264"[^}]*"master_url":\s*"([^"]+)"/,
        /"h264"[^}]*"master_url":\s*"([^"]+)"/,
        /"master_url":\s*"([^"]+\.mp4[^"]*)"/,
        /"backup_urls":\s*\[\s*"([^"]+\.mp4[^"]*)"/,
        // 更宽泛的模式
        /"master_url":\s*"([^"]+)"/,
        /"backup_urls":\s*\[\s*"([^"]+)"/,
        // 直接查找sns-video域名的URL
        /"(https?:[^"]*sns-video[^"]*\.mp4[^"]*)"/,
        /"(https?:[^"]*sns-video[^"]*)"/
      ];

      for (const pattern of videoPatterns) {
        const match = pageContent.match(pattern);
        if (match && match[1]) {
          let url = match[1];
          // 解码Unicode字符
          url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
          // 检查是否为视频URL（不是图片URL）
          if (url.includes('.mp4') || (url.includes('sns-') && !url.includes('imageView'))) {
            videoUrl = url;
            videoUrls.push(url); // 也添加到数组中
            addDebugLog('从传统JSON方法找到视频URL: ' + videoUrl);
            break;
          } else {
            addDebugLog('跳过非视频URL: ' + url);
          }
        }
      }

      // 如果还没找到，尝试查找其他格式
      if (!videoUrl) {
        const fallbackPatterns = [
          /"video"[^}]*"url":\s*"([^"]+)"/,
          /"stream"[^}]*"url":\s*"([^"]+)"/,
          /"media"[^}]*"url":\s*"([^"]+)"/
        ];

        for (const pattern of fallbackPatterns) {
          const match = pageContent.match(pattern);
          if (match && match[1]) {
            let url = match[1];
            // 解码Unicode字符
            url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            // 只接受明确的视频格式
            if (url.includes('.mp4') || url.includes('.m3u8')) {
              videoUrl = url;
              videoUrls.push(url); // 也添加到数组中
              addDebugLog('从备用模式找到视频URL: ' + videoUrl);
              break;
            }
          }
        }
      }
    }

    // 如果找到了videoUrl，先进行URL解码和格式化
    if (videoUrl && videoUrl !== realUrl) {
      // 解码Unicode字符
      const originalUrl = videoUrl;
      videoUrl = videoUrl.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
      addDebugLog('URL解码前: ' + originalUrl);
      addDebugLog('URL解码后: ' + videoUrl);

      // 检查是否为有效的视频URL
      if (videoUrl.includes('imageView') || videoUrl.includes('format/jpg') || videoUrl.includes('format/png')) {
        addDebugLog('检测到图片URL，不是视频URL，回退到页面URL');
        videoUrl = realUrl;
      }

      // 去水印处理
      const beforeWatermarkRemoval = videoUrl;
      videoUrl = removeWatermarkFromUrl(videoUrl);
      addDebugLog('=== 去水印处理 ===');
      addDebugLog('去水印前URL: ' + beforeWatermarkRemoval);
      addDebugLog('去水印后URL: ' + videoUrl);
      addDebugLog('=== 去水印处理结束 ===');
    }

    // 如果是视频内容但还是没找到视频URL，尝试API和构造URL方法
    if (contentType === 'video' && !videoUrl) {
      addDebugLog('视频内容但未找到视频URL，尝试API和构造URL方法...');

      // 方法1：尝试通过API获取
      try {
        addDebugLog('尝试通过API获取视频信息...');
        const apiResult = await getXhsNoteDetail(noteId);
        if (apiResult && apiResult.videoUrl) {
          const originalApiUrl = apiResult.videoUrl;
          videoUrl = removeWatermarkFromUrl(originalApiUrl);
          videoUrls.push(videoUrl); // 添加到数组中
          addDebugLog('=== API方法去水印处理 ===');
          addDebugLog('去水印前URL: ' + originalApiUrl);
          addDebugLog('去水印后URL: ' + videoUrl);
          addDebugLog('=== API方法去水印处理结束 ===');
          // 更新基本信息
          if (apiResult.title && apiResult.title !== '小红书内容') {
            basicInfo.title = apiResult.title;
          }
          if (apiResult.author && apiResult.author !== '小红书用户') {
            basicInfo.author = apiResult.author;
          }
        } else {
          addDebugLog('API返回了数据但没有视频URL');
        }
      } catch (apiError) {
        addDebugLog('API方法失败: ' + apiError.message);
      }

      // 方法2：如果API失败，尝试构造视频URL
      if (!videoUrl) {
        try {
          addDebugLog('尝试构造视频URL...');
          const constructResult = await tryConstructVideoUrl(noteId, realUrl);
          if (constructResult && constructResult.videoUrl) {
            const originalConstructUrl = constructResult.videoUrl;
            videoUrl = removeWatermarkFromUrl(originalConstructUrl);
            videoUrls.push(videoUrl); // 添加到数组中
            addDebugLog('=== 构造方法去水印处理 ===');
            addDebugLog('去水印前URL: ' + originalConstructUrl);
            addDebugLog('去水印后URL: ' + videoUrl);
            addDebugLog('=== 构造方法去水印处理结束 ===');
            // 更新基本信息
            if (constructResult.title && constructResult.title !== '小红书内容') {
              basicInfo.title = constructResult.title;
            }
            if (constructResult.author && constructResult.author !== '小红书用户') {
              basicInfo.author = constructResult.author;
            }
          }
        } catch (constructError) {
          addDebugLog('构造方法失败: ' + constructError.message);
        }
      }

      // 方法3：最后使用页面URL作为备用
      if (!videoUrl) {
        videoUrl = realUrl;
        addDebugLog('所有方法都失败，使用页面URL作为备用: ' + videoUrl);
      }
    }

    // 构建内容信息
    let contentInfo = {
      title: basicInfo.title,
      author: basicInfo.author,
      content: basicInfo.content || '', // 添加正文内容
      videoUrl: videoUrl,
      videoUrls: videoUrls, // 所有视频URL
      imageUrls: imageUrls, // 所有图片URL
      isDirectUrl: videoUrl === realUrl, // 标记是否为直接URL
      contentType: contentType
    };

    // 调试：输出提取到的内容信息
    addDebugLog('最终内容信息: ' + JSON.stringify(contentInfo, null, 2));

    // 第三步：构建返回结果
    let processedData, resultType, note;

    // 判断主要内容类型
    const hasImages = imageUrls.length > 0;
    const hasVideos = videoUrls.length > 0;

    if (contentType === 'image' || (hasImages && !hasVideos)) {
      // 纯图片内容
      processedData = {
        data: imageUrls[0], // 主图片URL（第一张图片）
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls, // 所有图片
        videoUrls: videoUrls, // 如果有Live Photo视频也提供
        isImageContent: true,
        hasLivePhoto: videoUrls.length > 0 // 标识是否有Live Photo
      };
      resultType = 'image';

      if (videoUrls.length > 0) {
        note = `已获取小红书图文内容（共${imageUrls.length}张图片，${videoUrls.length}个Live Photo视频）`;
      } else {
        note = `已获取小红书图文内容（共${imageUrls.length}张图片）`;
      }

      addDebugLog('构建返回结果 - 类型: ' + resultType + ', 图片数量: ' + imageUrls.length + ', 视频数量: ' + videoUrls.length);
    } else if (contentType === 'video' || (hasVideos && !hasImages)) {
      // 纯视频内容
      const isDirectVideo = videoUrl && (videoUrl.includes('.mp4') || videoUrl.includes('.m3u8'));

      processedData = {
        data: videoUrl,
        type: isDirectVideo ? 'video/mp4' : 'text/html',
        isUrl: true,
        isDirectUrl: contentInfo.isDirectUrl || false,
        videoUrls: videoUrls, // 所有视频URL
        imageUrls: imageUrls  // 如果有封面图也提供
      };
      resultType = 'video';
      note = isDirectVideo ?
        '已获取小红书视频直链' :
        '已获取小红书页面链接（可能需要在浏览器中打开）';

      addDebugLog('构建返回结果 - 类型: ' + resultType + ', 是否直接视频: ' + isDirectVideo + ', URL: ' + videoUrl);
    } else if (hasImages && hasVideos) {
      // 混合内容（Live Photo等）- 优先显示为图片内容
      processedData = {
        data: imageUrls[0], // 主图片URL
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: imageUrls, // 所有图片
        videoUrls: videoUrls, // 所有视频
        isImageContent: true,
        hasLivePhoto: true,
        isMixedContent: true // 标识为混合内容
      };
      resultType = 'image';
      note = `已获取小红书混合内容（${imageUrls.length}张图片，${videoUrls.length}个视频）`;

      addDebugLog('构建返回结果 - 类型: 混合内容, 图片数量: ' + imageUrls.length + ', 视频数量: ' + videoUrls.length);
    } else {
      // 没有找到内容
      processedData = {
        data: null,
        type: 'unknown',
        isUrl: false
      };
      resultType = 'unknown';
      note = '无法获取内容';

      addDebugLog('未找到任何内容');
    }

    const result = {
      title: contentInfo.title,
      author: contentInfo.author,
      content: contentInfo.content || '', // 添加正文内容
      processedData: processedData,
      type: resultType, // 'video' 或 'image'
      platform: 'xiaohongshu',
      source: '小红书',
      note: note,
      originalUrl: shareUrl,
      noteId: noteId
    };

    // 如果保存了HTML文件，添加文件信息
    if (htmlFileInfo) {
      result.htmlFile = htmlFileInfo;
      result.note += ' (已保存HTML源码)';
    }

    return result;

  } catch (error) {
    console.error('小红书解析失败:', error);
    throw error;
  }
}

// 获取真实URL（优化版本）
async function getRealUrl(shareUrl) {
  try {
    console.log('开始获取真实URL:', shareUrl);

    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,
      timeout: 15000, // 增加超时时间
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
      }
    });

    console.log('响应状态码:', response.status);
    console.log('响应头:', response.headers);

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;

    console.log('真实URL:', realUrl);
    return realUrl;

  } catch (error) {
    console.error('获取真实URL失败:', error);
    console.log('使用原始URL:', shareUrl);
    return shareUrl;
  }
}

// 提取note_id
function extractNoteId(url) {
  try {
    addDebugLog('从URL提取note_id: ' + url);

    // 模式1: xiaohongshu.com/explore/note_id
    let match = url.match(/xiaohongshu\.com\/explore\/([a-f0-9]+)/i);
    if (match) {
      addDebugLog('模式1匹配成功: ' + match[1]);
      return match[1];
    }

    // 模式2: xiaohongshu.com/discovery/item/note_id
    match = url.match(/xiaohongshu\.com\/discovery\/item\/([a-f0-9]+)/i);
    if (match) {
      addDebugLog('模式2匹配成功: ' + match[1]);
      return match[1];
    }

    // 模式3: 从URL参数中提取
    match = url.match(/[?&]id=([a-f0-9]+)/i);
    if (match) {
      addDebugLog('模式3匹配成功: ' + match[1]);
      return match[1];
    }

    // 模式4: 任何24位十六进制字符串
    match = url.match(/([a-f0-9]{24})/i);
    if (match) {
      addDebugLog('模式4匹配成功: ' + match[1]);
      return match[1];
    }

    addDebugLog('未能提取到note_id');
    return null;
  } catch (error) {
    addDebugLog('提取note_id失败: ' + error.message);
    return null;
  }
}

// 从URL中提取内容类型
function extractContentType(url) {
  try {
    addDebugLog('从URL提取内容类型: ' + url);

    // 检查URL参数中的type
    const typeMatch = url.match(/[?&]type=([^&]+)/i);
    if (typeMatch) {
      const type = typeMatch[1].toLowerCase();
      addDebugLog('URL参数type: ' + type);

      if (type === 'video') {
        addDebugLog('URL参数判断为视频内容');
        return 'video';
      } else if (type === 'normal') {
        addDebugLog('URL参数判断为图文内容');
        return 'image';
      } else if (type === 'image') {
        addDebugLog('URL参数判断为图片内容');
        return 'image';
      }
    }

    // 如果没有type参数，返回unknown
    addDebugLog('未找到type参数，返回unknown');
    return 'unknown';
  } catch (error) {
    addDebugLog('提取内容类型失败: ' + error.message);
    return 'unknown';
  }
}

// 尝试构造视频URL（针对type=video的内容）
async function tryConstructVideoUrl(noteId, realUrl) {
  try {
    addDebugLog('开始构造视频URL，note_id: ' + noteId);

    // 小红书视频URL的常见模式
    const videoCdnDomains = [
      'sns-video-bd.xhscdn.com',
      'sns-video-qc.xhscdn.com',
      'sns-video-hw.xhscdn.com',
      'sns-video-al.xhscdn.com'
    ];

    // 视频质量后缀
    const qualitySuffixes = [
      '_1080p',
      '_720p',
      '_480p',
      ''  // 无后缀
    ];

    // 构造可能的视频URL
    const possibleUrls = [];
    for (const domain of videoCdnDomains) {
      for (const suffix of qualitySuffixes) {
        possibleUrls.push(`https://${domain}/${noteId}${suffix}.mp4`);
      }
    }

    addDebugLog('构造了 ' + possibleUrls.length + ' 个可能的视频URL');
    addDebugLog('测试前3个URL: ' + possibleUrls.slice(0, 3).join(', '));

    // 测试哪个URL有效
    for (let i = 0; i < possibleUrls.length; i++) {
      const testUrl = possibleUrls[i];
      try {
        addDebugLog('测试视频URL ' + (i + 1) + ': ' + testUrl);

        const response = await uniCloud.httpclient.request(testUrl, {
          method: 'HEAD',
          timeout: 5000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
            'Referer': 'https://www.xiaohongshu.com/'
          }
        });

        addDebugLog('URL测试响应状态: ' + response.status);

        if (response.status === 200) {
          addDebugLog('找到有效视频URL: ' + testUrl);

          // 去水印处理
          const cleanUrl = removeWatermarkFromUrl(testUrl);
          addDebugLog('=== 构造URL去水印处理 ===');
          addDebugLog('去水印前URL: ' + testUrl);
          addDebugLog('去水印后URL: ' + cleanUrl);
          addDebugLog('=== 构造URL去水印处理结束 ===');

          // 尝试获取标题和作者信息
          let title = '小红书视频';
          let author = '小红书用户';

          try {
            const pageContent = await getPageContent(realUrl);
            const pageInfo = await extractBasicInfo(pageContent);
            title = pageInfo.title || title;
            author = pageInfo.author || author;
          } catch (e) {
            addDebugLog('获取页面基本信息失败: ' + e.message);
          }

          return {
            title: title,
            author: author,
            videoUrl: cleanUrl,
            imageUrls: []
          };
        }
      } catch (e) {
        addDebugLog('URL测试失败: ' + e.message);
        continue;
      }
    }

    addDebugLog('所有视频URL测试失败，尝试页面解析');

    // 如果直接构造失败，回退到页面解析
    const pageContent = await getPageContent(realUrl);
    return await extractXiaohongshuInfo(pageContent, noteId);

  } catch (error) {
    addDebugLog('构造视频URL失败: ' + error.message);
    throw error;
  }
}

// 提取页面基本信息（标题、作者和正文）- 优先HTML元素
async function extractBasicInfo(html, noteId = null) {
  try {
    let title = '小红书内容';
    let author = '小红书用户';
    let content = '';

    console.log('开始提取基本信息...');
    console.log('HTML长度:', html.length);

    // 提取作者信息 - 只使用nickName字段
    console.log('开始提取作者信息...');

    // 直接从pageContent中查找nickName（笔记作者字段）
    // 根据调试日志，笔记作者的数据结构是：
    // "user":{"nickName":"作者昵称","avatar":"...","userId":"..."}
    const nickNameMatch = html.match(/"nickName":\s*"([^"]+)"/);
    if (nickNameMatch && nickNameMatch[1]) {
      author = nickNameMatch[1];
      console.log('✅ 从nickName字段提取到作者:', author);
    } else {
      author = '未知作者';
      console.log('❌ 未找到nickName字段，设置为未知作者');
    }

    console.log('=== 作者提取完成 ===');

    // 提取标题和正文内容
    console.log('开始提取标题和正文内容...');

    // 方法1：先尝试从JSON中提取title和desc
    let foundTitle = false;
    let foundContent = false;

    // 提取标题
    const titleMatch = html.match(/"title":\s*"([^"]+)"/);
    if (titleMatch && titleMatch[1] && titleMatch[1].length > 2 && titleMatch[1] !== '小红书') {
      title = titleMatch[1];
      foundTitle = true;
      console.log('从JSON title字段提取到标题:', title);
    }

    // 提取正文内容 - 尝试多种字段名
    const contentPatterns = [
      /"desc":\s*"([^"]*(?:\\.[^"]*)*)"/,  // 主要的desc字段
      /"content":\s*"([^"]*(?:\\.[^"]*)*)"/,  // content字段
      /"description":\s*"([^"]*(?:\\.[^"]*)*)"/,  // description字段
      /"text":\s*"([^"]*(?:\\.[^"]*)*)"/   // text字段
    ];

    for (const pattern of contentPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        let rawContent = match[1];
        // 解码Unicode和转义字符
        rawContent = rawContent
          .replace(/\\"/g, '"')
          .replace(/\\n/g, '\n')
          .replace(/\\r/g, '\r')
          .replace(/\\t/g, '\t')
          .replace(/\\\\/g, '\\')
          .replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => String.fromCharCode(parseInt(code, 16)))
          .trim();
        
        if (rawContent && rawContent.length > 5) {
          content = rawContent;
          foundContent = true;
          console.log('从JSON提取到正文内容（前50字符）:', content.substring(0, 50) + '...');
          break;
        }
      }
    }

    // 方法2：如果没有找到标题，从页面title标签提取
    if (!foundTitle) {
      const pageTitleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (pageTitleMatch) {
        const rawTitle = pageTitleMatch[1].replace(/\s*-\s*小红书.*$/, '').trim();
        if (rawTitle && rawTitle !== '小红书' && rawTitle.length > 2) {
          title = rawTitle;
          console.log('从页面title提取到标题:', title);
        }
      }
    }

    // 方法3：如果没有找到正文，尝试从meta描述中提取
    if (!foundContent) {
      const metaDescMatch = html.match(/<meta\s+name=["']description["']\s+content=["']([^"']+)["']/i);
      if (metaDescMatch && metaDescMatch[1]) {
        content = metaDescMatch[1].trim();
        console.log('从meta description提取到内容:', content.substring(0, 50) + '...');
      }
    }

    console.log('最终提取结果 - 标题:', title, ', 作者:', author, ', 正文长度:', content.length);
    return { title, author, content };
  } catch (error) {
    console.error('提取基本信息失败:', error);
    return { title: '小红书内容', author: '小红书用户', content: '' };
  }
}





// 通过API获取小红书笔记详情
async function getXhsNoteDetail(noteId) {
  try {
    console.log('尝试通过API获取笔记详情:', noteId);

    // 构造小红书API URL（这是一个常见的API端点）
    const apiUrl = `https://www.xiaohongshu.com/api/sns/web/v1/feed`;

    const response = await uniCloud.httpclient.request(apiUrl, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Referer': 'https://www.xiaohongshu.com/',
        'Origin': 'https://www.xiaohongshu.com'
      },
      data: {
        source_note_id: noteId,
        image_formats: ['jpg', 'webp', 'avif'],
        extra: { need_body_topic: 1 },
        xsec_source: 'pc_feed',
        xsec_token: ''
      },
      timeout: 10000
    });

    console.log('API响应状态:', response.status);

    if (response.status === 200 && response.data) {
      const data = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
      console.log('API返回数据结构:', Object.keys(data));

      if (data.data && data.data.items && data.data.items.length > 0) {
        const noteData = data.data.items[0].note_card;
        return parseNoteCardData(noteData);
      }
    }

    throw new Error('API返回数据格式不正确');

  } catch (error) {
    console.error('API获取失败:', error);
    throw error;
  }
}

// 解析笔记卡片数据
function parseNoteCardData(noteCard) {
  try {
    console.log('解析笔记卡片数据');

    const result = {
      title: noteCard.display_title || noteCard.title || '小红书内容',
      author: noteCard.user?.nickname || '小红书用户',
      videoUrl: null,
      imageUrls: []
    };

    // 解析视频
    if (noteCard.video && noteCard.video.media && noteCard.video.media.stream) {
      const videoStream = noteCard.video.media.stream;
      if (videoStream.h264 && videoStream.h264.length > 0) {
        // 选择最高质量的视频
        const bestVideo = videoStream.h264.reduce((prev, current) =>
          (prev.quality > current.quality) ? prev : current
        );
        result.videoUrl = bestVideo.master_url || bestVideo.backup_urls[0];
        console.log('找到视频URL:', result.videoUrl);
      }
    }

    // 解析图片
    if (noteCard.image_list && noteCard.image_list.length > 0) {
      result.imageUrls = noteCard.image_list.map(img => {
        if (img.url_size_large) {
          return img.url_size_large;
        } else if (img.url_default) {
          return img.url_default;
        }
        return null;
      }).filter(Boolean);
      console.log('找到图片:', result.imageUrls.length, '张');
    }

    return result;

  } catch (error) {
    console.error('解析笔记卡片数据失败:', error);
    throw error;
  }
}

// 获取页面内容（参考抖音解析器）
async function getPageContent(url) {
  try {
    console.log('获取页面内容:', url);

    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 15000,
      dataType: 'text',
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.xiaohongshu.com/'
      }
    });

    console.log('页面获取成功，状态:', response.status);
    console.log('页面内容长度:', response.data ? response.data.length : 0);

    // 确保返回字符串
    let htmlContent = response.data;
    if (typeof htmlContent !== 'string') {
      if (htmlContent && htmlContent.toString) {
        htmlContent = htmlContent.toString();
      } else {
        throw new Error('页面内容格式错误');
      }
    }

    // 调试：输出页面内容的关键部分
    console.log('页面标题部分:', htmlContent.substring(0, 1000));
    console.log('页面是否包含笔记数据:', htmlContent.includes('window.__INITIAL_STATE__'));
    console.log('页面是否包含视频相关:', htmlContent.includes('video'));
    console.log('页面是否包含图片相关:', htmlContent.includes('images'));

    // 查找所有可能的视频相关字符串
    const videoKeywords = ['stream', 'h264', 'master_url', 'backup_urls', 'sns-video', '.mp4', '.m3u8'];
    videoKeywords.forEach(keyword => {
      const count = (htmlContent.match(new RegExp(keyword, 'gi')) || []).length;
      console.log(`关键词 "${keyword}" 出现次数:`, count);
    });

    // 查找JSON数据块
    const jsonMatch = htmlContent.match(/window\.__INITIAL_STATE__\s*=\s*({.+?});/);
    if (jsonMatch) {
      console.log('找到INITIAL_STATE数据块，长度:', jsonMatch[1].length);
      // 输出前500字符看看结构
      console.log('INITIAL_STATE前500字符:', jsonMatch[1].substring(0, 500));
    }

    return htmlContent;

  } catch (error) {
    console.error('获取页面内容失败:', error);
    throw new Error('无法获取页面内容: ' + error.message);
  }
}

// 提取小红书信息（页面解析备用方案）
async function extractXiaohongshuInfo(html, noteId) {
  try {
    console.log('开始提取小红书信息...');
    console.log('HTML内容类型:', typeof html);
    console.log('HTML内容长度:', html ? html.length : 0);

    // 确保html是字符串
    if (typeof html !== 'string') {
      throw new Error('HTML内容不是字符串格式');
    }

    if (!html || html.length < 100) {
      throw new Error('HTML内容太短，可能获取失败');
    }

    addDebugLog('开始页面解析提取小红书信息...');
    addDebugLog('note_id: ' + noteId);
    addDebugLog('HTML内容长度: ' + html.length);
    addDebugLog('HTML内容片段（前500字符）: ' + html.substring(0, 500));

    // 分析页面内容特征
    addDebugLog('页面内容特征分析:');
    addDebugLog('- 包含__INITIAL_STATE__: ' + html.includes('__INITIAL_STATE__'));
    addDebugLog('- 包含window.__INITIAL_STATE__: ' + html.includes('window.__INITIAL_STATE__'));
    addDebugLog('- 包含sns-video: ' + html.includes('sns-video'));
    addDebugLog('- 包含xhscdn: ' + html.includes('xhscdn'));
    addDebugLog('- 包含.mp4: ' + html.includes('.mp4'));
    addDebugLog('- 包含video: ' + html.includes('video'));
    addDebugLog('- 包含' + noteId + ': ' + html.includes(noteId));

    // 尝试从页面中找到JSON数据
    addDebugLog('查找页面中的JSON数据...');

    // 提取标题
    let title = '小红书内容';
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      title = titleMatch[1].replace(/\s*-\s*小红书.*$/, '').trim();
      console.log('提取到标题:', title);
    }

    // 提取作者
    let author = '小红书用户';
    const authorPatterns = [
      /"nickname":\s*"([^"]+)"/,
      /"author":\s*"([^"]+)"/,
      /"user":\s*{[^}]*"nickname":\s*"([^"]+)"/
    ];

    for (const pattern of authorPatterns) {
      const match = html.match(pattern);
      if (match) {
        author = match[1];
        console.log('提取到作者:', author);
        break;
      }
    }

    // 尝试从页面的window.__INITIAL_STATE__中提取数据
    let videoUrl = null;
    let imageUrls = [];

    const initialStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.+?});/);
    if (initialStateMatch) {
      try {
        addDebugLog('找到__INITIAL_STATE__匹配，长度: ' + initialStateMatch[1].length);
        const initialState = JSON.parse(initialStateMatch[1]);
        addDebugLog('成功解析__INITIAL_STATE__数据，顶级键: ' + Object.keys(initialState).join(', '));

        // 查找笔记数据
        const noteData = findNoteInState(initialState, noteId);
        if (noteData) {
          addDebugLog('在__INITIAL_STATE__中找到笔记数据，键: ' + Object.keys(noteData).join(', '));

          // 提取视频
          if (noteData.video && noteData.video.media) {
            const videoMedia = noteData.video.media;
            if (videoMedia.stream && videoMedia.stream.h264) {
              const h264List = videoMedia.stream.h264;
              if (h264List.length > 0) {
                videoUrl = h264List[0].master_url || h264List[0].backup_urls[0];
                console.log('从__INITIAL_STATE__找到视频:', videoUrl);
              }
            }
          }

          // 提取图片
          if (noteData.image_list && noteData.image_list.length > 0) {
            imageUrls = noteData.image_list.map(img =>
              img.url_size_large || img.url_default || img.url
            ).filter(Boolean);
            console.log('从__INITIAL_STATE__找到图片:', imageUrls.length, '张');
          }

          // 更新标题和作者
          if (noteData.title) title = noteData.title;
          if (noteData.user && noteData.user.nickname) author = noteData.user.nickname;
        }
      } catch (e) {
        console.log('解析__INITIAL_STATE__失败:', e.message);
      }
    }

    // 如果__INITIAL_STATE__没有找到，尝试其他方法
    if (!videoUrl && imageUrls.length === 0) {
      console.log('__INITIAL_STATE__未找到数据，尝试其他方法...');

      // 方法2：直接查找视频URL
      const mp4Matches = html.match(/https?:\/\/[^"'\s]*sns-video[^"'\s]*\.mp4[^"'\s]*/g);
      if (mp4Matches && mp4Matches.length > 0) {
        videoUrl = mp4Matches[0];
        console.log('直接匹配找到视频:', videoUrl);
      }
    }

    // 如果还是没有找到图片，尝试直接匹配
    if (imageUrls.length === 0) {
      console.log('尝试直接匹配图片URL...');
      const imgMatches = html.match(/https?:\/\/[^"'\s]*xhscdn[^"'\s]*\.(?:jpg|jpeg|png|webp)[^"'\s]*/gi);
      if (imgMatches) {
        imageUrls = [...new Set(imgMatches)].filter(url =>
          !url.includes('avatar') && !url.includes('icon')
        ).slice(0, 9);
        console.log('直接匹配找到图片:', imageUrls.length, '张');
      }
    }

    return {
      title: title,
      author: author,
      videoUrl: videoUrl,
      imageUrls: imageUrls
    };

  } catch (error) {
    console.error('提取小红书信息失败:', error);
    throw error;
  }
}

// 在__INITIAL_STATE__中查找笔记数据
function findNoteInState(state, noteId) {
  try {
    addDebugLog('在state中查找note_id: ' + noteId);
    addDebugLog('state顶级结构: ' + Object.keys(state).join(', '));

    // 递归查找包含指定noteId的数据
    function searchObject(obj, path = '', depth = 0) {
      if (!obj || typeof obj !== 'object' || depth > 10) return null;

      // 检查当前对象是否是我们要找的笔记
      if (obj.note_id === noteId || obj.id === noteId) {
        addDebugLog('找到匹配的笔记数据，路径: ' + path);
        addDebugLog('笔记数据键: ' + Object.keys(obj).join(', '));
        return obj;
      }

      // 如果是数组，检查每个元素
      if (Array.isArray(obj)) {
        for (let i = 0; i < obj.length; i++) {
          const result = searchObject(obj[i], `${path}[${i}]`, depth + 1);
          if (result) return result;
        }
      } else {
        // 递归搜索对象属性
        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'object' && value !== null) {
            const result = searchObject(value, `${path}.${key}`, depth + 1);
            if (result) return result;
          }
        }
      }

      return null;
    }

    const result = searchObject(state);
    if (!result) {
      addDebugLog('未在__INITIAL_STATE__中找到匹配的笔记数据');
    }
    return result;
  } catch (error) {
    addDebugLog('查找笔记数据失败: ' + error.message);
    return null;
  }
}



