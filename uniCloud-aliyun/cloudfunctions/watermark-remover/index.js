'use strict';

const crypto = require('crypto');

exports.main = async (event, context) => {
  console.log('event : ', event);
  
  const { type, content } = event;
  
  try {
    switch (type) {
      case 'link':
        return await processLink(content);
      case 'file':
        return await processFile(content);
      default:
        return {
          success: false,
          message: '不支持的处理类型'
        };
    }
  } catch (error) {
    console.error('处理失败:', error);
    return {
      success: false,
      message: error.message || '处理失败'
    };
  }
};

// 处理链接
async function processLink(link) {
  console.log('处理链接:', link);
  
  // 检测平台类型
  const platform = detectPlatform(link);
  if (!platform) {
    throw new Error('不支持的链接格式');
  }
  
  // 提取真实链接
  const realUrl = await extractRealUrl(link, platform);
  
  // 解析视频信息
  const videoInfo = await parseVideoInfo(realUrl, platform);
  
  return {
    success: true,
    data: {
      title: videoInfo.title,
      author: videoInfo.author,
      downloadUrl: videoInfo.downloadUrl,
      coverUrl: videoInfo.coverUrl,
      platform: platform,
      type: platform,
      source: getPlatformName(platform)
    }
  };
}

// 处理文件
async function processFile(filePath) {
  console.log('处理文件:', filePath);
  
  // 这里可以添加文件处理逻辑
  // 比如去除图片水印、视频水印等
  
  return {
    success: true,
    data: {
      title: '本地文件处理',
      downloadUrl: filePath,
      type: 'local',
      source: '本地文件'
    }
  };
}

// 检测平台类型
function detectPlatform(link) {
  if (/douyin\.com|dy\.com/i.test(link)) {
    return 'douyin';
  } else if (/kuaishou\.com|ks\.com|v\.kuaishou\.com|kwai\.app|ks\.app|gifshow\.com/i.test(link)) {
    return 'kuaishou';
  } else if (/xiaohongshu\.com|xhslink\.com/i.test(link)) {
    return 'xiaohongshu';
  }
  return null;
}

// 获取平台名称
function getPlatformName(platform) {
  const names = {
    'douyin': '抖音',
    'kuaishou': '快手',
    'xiaohongshu': '小红书'
  };
  return names[platform] || '未知平台';
}

// 提取真实链接
async function extractRealUrl(shareLink, platform) {
  // 这里需要实现具体的链接解析逻辑
  // 由于涉及到第三方平台的接口，这里提供一个模拟实现
  
  console.log(`解析${platform}链接:`, shareLink);
  
  // 模拟解析过程
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 返回模拟的真实链接
  return `https://api.${platform}.com/video/12345`;
}

// 解析视频信息 - 使用自建爬虫
async function parseVideoInfo(url, platform) {
  console.log(`解析${platform}视频信息:`, url);

  try {
    // 根据平台调用不同的解析方法
    switch (platform) {
      case 'douyin':
        return await parseDouyinByCrawler(url);
      case 'kuaishou':
        return await parseKuaishouByCrawler(url);
      case 'xiaohongshu':
        return await parseXiaohongshuByCrawler(url);
      default:
        throw new Error('不支持的平台');
    }
  } catch (error) {
    console.error('爬虫解析失败:', error);
    throw new Error('解析失败: ' + error.message);
  }
}

// 抖音爬虫解析
async function parseDouyinByCrawler(shareUrl) {
  try {
    // 第一步：获取真实URL
    const realUrl = await getRealUrl(shareUrl);
    console.log('真实URL:', realUrl);

    // 第二步：解析页面获取视频信息
    const videoInfo = await crawlDouyinPage(realUrl);

    // 第三步：获取无水印视频流
    const processedVideo = await processVideoStream(videoInfo.videoUrl);

    return {
      title: videoInfo.title,
      author: videoInfo.author,
      // 不返回下载链接，而是返回处理后的视频数据
      processedData: processedVideo,
      type: 'video',
      platform: 'douyin'
    };
  } catch (error) {
    console.error('抖音解析失败:', error);
    throw error;
  }
}

// 获取真实URL（处理短链接跳转）
async function getRealUrl(shortUrl) {
  try {
    const response = await uniCloud.httpclient.request(shortUrl, {
      method: 'GET',
      followRedirect: false, // 不自动跟随重定向
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
      }
    });

    // 从重定向响应中获取真实URL
    const location = response.headers.location;
    if (location) {
      return location;
    }

    throw new Error('无法获取真实URL');
  } catch (error) {
    console.error('获取真实URL失败:', error);
    throw error;
  }
}

// 爬取抖音页面信息
async function crawlDouyinPage(url) {
  try {
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate'
      }
    });

    const html = response.data;

    // 使用正则表达式提取视频信息
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*-\s*抖音$/, '') : '未知标题';

    // 提取作者信息
    const authorMatch = html.match(/"nickname":"([^"]+)"/);
    const author = authorMatch ? authorMatch[1] : '未知作者';

    // 提取视频URL
    const videoUrlMatch = html.match(/"play_addr":\{"url_list":\["([^"]+)"/);
    const videoUrl = videoUrlMatch ? videoUrlMatch[1].replace(/\\u002F/g, '/') : null;

    if (!videoUrl) {
      throw new Error('无法提取视频URL');
    }

    return {
      title,
      author,
      videoUrl
    };
  } catch (error) {
    console.error('爬取页面失败:', error);
    throw error;
  }
}

// 处理视频流（去水印）
async function processVideoStream(videoUrl) {
  try {
    // 获取原始视频流
    const videoResponse = await uniCloud.httpclient.request(videoUrl, {
      method: 'GET',
      timeout: 30000,
      dataType: 'buffer', // 获取二进制数据
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15',
        'Referer': 'https://www.douyin.com/'
      }
    });

    // 这里可以添加视频处理逻辑
    // 例如：去除水印、压缩、格式转换等
    const processedBuffer = await removeWatermark(videoResponse.data);

    // 转换为base64返回（避免存储文件）
    const base64Data = processedBuffer.toString('base64');

    return {
      data: base64Data,
      type: 'video/mp4',
      size: processedBuffer.length
    };
  } catch (error) {
    console.error('处理视频流失败:', error);
    throw error;
  }
}

// 去水印处理（简化版本）
async function removeWatermark(videoBuffer) {
  // TODO: 这里可以集成视频处理库
  // 例如：ffmpeg、opencv等
  // 当前返回原始数据
  return videoBuffer;
}

// 解析抖音视频
async function parseDouyinVideo(shareUrl, apiConfig) {
  const response = await uniCloud.httpclient.request(apiConfig.douyin, {
    method: 'GET',
    data: {
      text: shareUrl,
      key: apiConfig.key
    },
    timeout: 10000
  });

  if (response.data.error_code === 0) {
    const result = response.data.result;
    return {
      title: result.title,
      author: result.author.nickname,
      downloadUrl: result.video.play_addr.url_list[0],
      coverUrl: result.video.cover.url_list[0]
    };
  }

  throw new Error(response.data.reason || '抖音解析失败');
}

// 快手爬虫解析
async function parseKuaishouByCrawler(shareUrl) {
  try {
    console.log('调用快手专用解析器:', shareUrl);
    
    // 调用专门的快手解析云函数
    const result = await uniCloud.callFunction({
      name: 'simple-kuaishou-parser',
      data: {
        link: shareUrl
      }
    });

    if (!result.result || !result.result.success) {
      throw new Error(result.result?.message || '快手解析失败');
    }

    const data = result.result.data;
    
    return {
      title: data.title,
      author: data.author,
      downloadUrl: data.processedData?.data,
      coverUrl: data.coverUrl,
      processedData: data.processedData,
      type: data.type,
      platform: 'kuaishou'
    };
    
  } catch (error) {
    console.error('快手解析失败:', error);
    throw new Error('快手解析失败: ' + error.message);
  }
}

// 小红书爬虫解析
async function parseXiaohongshuByCrawler(shareUrl) {
  try {
    // 第一步：获取真实URL
    const realUrl = await getRealUrl(shareUrl);
    console.log('小红书真实URL:', realUrl);

    // 第二步：解析页面获取内容信息
    const contentInfo = await crawlXiaohongshuPage(realUrl);

    // 第三步：处理内容（去水印等）
    const processedContent = await processXiaohongshuContent(contentInfo);

    return {
      title: contentInfo.title,
      author: contentInfo.author,
      processedData: processedContent,
      type: contentInfo.contentType || 'unknown',
      platform: 'xiaohongshu'
    };
  } catch (error) {
    console.error('小红书解析失败:', error);
    throw error;
  }
}

// 爬取小红书页面信息
async function crawlXiaohongshuPage(url) {
  try {
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://www.xiaohongshu.com/',
        'Connection': 'keep-alive'
      }
    });

    const html = response.data;
    console.log('小红书页面内容长度:', html.length);

    // 解析页面内容
    return parseXiaohongshuPageContent(html, url);

  } catch (error) {
    console.error('爬取小红书页面失败:', error);
    throw new Error('获取小红书页面内容失败: ' + error.message);
  }
}

// 解析小红书页面内容
function parseXiaohongshuPageContent(html, url) {
  try {
    console.log('开始解析小红书页面内容');

    // 提取基本信息
    let title = '小红书内容';
    let author = '未知作者';
    let contentType = 'unknown';
    let videoUrl = null;
    let imageUrls = [];

    // 方法1：从window.__INITIAL_STATE__中提取
    const initialStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.+?});/);
    if (initialStateMatch) {
      try {
        const initialState = JSON.parse(initialStateMatch[1]);
        console.log('找到小红书初始状态数据');

        // 查找笔记数据
        const noteData = findXiaohongshuNoteData(initialState);
        if (noteData) {
          return parseXiaohongshuNoteData(noteData);
        }
      } catch (e) {
        console.log('解析小红书初始状态失败:', e.message);
      }
    }

    // 方法2：从页面标题提取
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      title = titleMatch[1].replace(/\s*-\s*小红书.*$/, '').trim();
    }

    // 方法3：从script标签中查找数据
    const scriptMatches = html.match(/<script[^>]*>[\s\S]*?<\/script>/gi);
    if (scriptMatches) {
      for (const script of scriptMatches) {
        const jsonMatch = script.match(/({[\s\S]*"noteId"[\s\S]*?})/);
        if (jsonMatch) {
          try {
            const data = JSON.parse(jsonMatch[1]);
            const noteData = findXiaohongshuNoteDataInObject(data);
            if (noteData) {
              return parseXiaohongshuNoteData(noteData);
            }
          } catch (e) {
            continue;
          }
        }
      }
    }

    // 如果都没找到，返回基本信息
    return {
      title,
      author,
      contentType,
      videoUrl,
      imageUrls,
      message: '无法获取详细内容信息'
    };

  } catch (error) {
    console.error('解析小红书页面内容失败:', error);
    throw error;
  }
}

// 在初始状态中查找笔记数据
function findXiaohongshuNoteData(obj, path = '') {
  if (!obj || typeof obj !== 'object') return null;

  // 检查当前对象是否包含笔记数据
  if (obj.noteId || obj.note_id) {
    console.log('找到小红书笔记数据:', path);
    return obj;
  }

  // 递归查找
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'object' && value !== null) {
      const result = findXiaohongshuNoteData(value, path + '.' + key);
      if (result) return result;
    }
  }

  return null;
}

// 在对象中查找笔记数据
function findXiaohongshuNoteDataInObject(obj) {
  if (!obj || typeof obj !== 'object') return null;

  // 常见的笔记数据字段
  const noteFields = ['noteId', 'note_id', 'id', 'title', 'desc', 'video', 'imageList'];

  if (noteFields.some(field => obj.hasOwnProperty(field))) {
    return obj;
  }

  // 递归查找
  for (const value of Object.values(obj)) {
    if (typeof value === 'object' && value !== null) {
      const result = findXiaohongshuNoteDataInObject(value);
      if (result) return result;
    }
  }

  return null;
}

// 解析笔记数据
function parseXiaohongshuNoteData(noteData) {
  try {
    console.log('解析小红书笔记数据:', Object.keys(noteData));

    const result = {
      title: noteData.title || noteData.desc || '小红书内容',
      author: noteData.user?.nickname || noteData.author?.nickname || '未知作者',
      contentType: 'unknown',
      duration: 0,
      videoUrl: null,
      imageUrls: []
    };

    // 判断内容类型并处理
    if (noteData.video || noteData.videoUrl) {
      // 视频内容
      result.contentType = 'video';
      const videoUrl = noteData.video?.url || noteData.videoUrl || noteData.video?.playUrl;

      if (videoUrl) {
        result.videoUrl = processXiaohongshuVideoUrl(videoUrl);
        result.duration = noteData.video?.duration || 0;
      }
    } else if (noteData.imageList || noteData.images) {
      // 图文内容
      result.contentType = 'image';
      const images = noteData.imageList || noteData.images || [];
      result.imageUrls = images.map(img => img.url || img.src || img).filter(Boolean);
      result.videoUrl = result.imageUrls[0]; // 主图片作为预览
      result.isImageContent = true;
    }

    console.log('小红书解析结果:', result);
    return result;

  } catch (error) {
    console.error('解析小红书笔记数据失败:', error);
    throw error;
  }
}

// 处理小红书视频URL（去水印）
function processXiaohongshuVideoUrl(videoUrl) {
  try {
    console.log('处理小红书视频URL:', videoUrl);

    // 小红书视频去水印处理
    let cleanUrl = videoUrl;

    // 移除水印相关参数
    cleanUrl = cleanUrl.replace(/[?&]watermark=[^&]*/g, '');
    cleanUrl = cleanUrl.replace(/[?&]wm=[^&]*/g, '');

    // 确保URL格式正确
    if (!cleanUrl.startsWith('http')) {
      cleanUrl = 'https:' + cleanUrl;
    }

    console.log('处理后的小红书视频URL:', cleanUrl);
    return cleanUrl;

  } catch (error) {
    console.error('处理小红书视频URL失败:', error);
    return videoUrl;
  }
}

// 处理小红书内容
async function processXiaohongshuContent(contentInfo) {
  try {
    if (contentInfo.contentType === 'video' && contentInfo.videoUrl) {
      // 处理视频内容
      return await processXiaohongshuVideoStream(contentInfo.videoUrl);
    } else if (contentInfo.contentType === 'image' && contentInfo.imageUrls?.length > 0) {
      // 处理图片内容
      return await processXiaohongshuImages(contentInfo.imageUrls);
    }

    return null;
  } catch (error) {
    console.error('处理小红书内容失败:', error);
    return null;
  }
}

// 处理小红书视频流
async function processXiaohongshuVideoStream(videoUrl) {
  try {
    console.log('开始处理小红书视频流:', videoUrl);

    const response = await uniCloud.httpclient.request(videoUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
        'Referer': 'https://www.xiaohongshu.com/'
      },
      timeout: 30000,
      dataType: 'buffer'
    });

    // 这里可以添加视频处理逻辑（去水印等）
    const processedData = response.data;

    // 转换为base64
    const base64Data = processedData.toString('base64');

    return base64Data;

  } catch (error) {
    console.error('处理小红书视频流失败:', error);
    throw error;
  }
}

// 处理小红书图片
async function processXiaohongshuImages(imageUrls) {
  try {
    console.log('开始处理小红书图片:', imageUrls.length, '张');

    const processedImages = [];

    for (const imageUrl of imageUrls.slice(0, 9)) { // 最多处理9张图片
      try {
        const response = await uniCloud.httpclient.request(imageUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
            'Referer': 'https://www.xiaohongshu.com/'
          },
          timeout: 15000,
          dataType: 'buffer'
        });

        // 转换为base64
        const base64Data = response.data.toString('base64');
        processedImages.push(base64Data);

      } catch (error) {
        console.error('处理图片失败:', imageUrl, error.message);
        continue;
      }
    }

    return processedImages;

  } catch (error) {
    console.error('处理小红书图片失败:', error);
    throw error;
  }
}

// 解析快手视频（需要配置对应的API）
async function parseKuaishouVideo(shareUrl) {
  // TODO: 实现快手解析逻辑
  throw new Error('快手解析功能开发中');
}

// 解析小红书内容（需要配置对应的API）
async function parseXiaohongshuVideo(shareUrl) {
  // TODO: 实现小红书解析逻辑
  throw new Error('小红书解析功能开发中');
}

// 生成唯一ID
function generateId() {
  return crypto.randomBytes(16).toString('hex');
}

// 验证链接格式
function validateLink(link) {
  if (!link || typeof link !== 'string') {
    return false;
  }
  
  // 基本的URL格式验证
  try {
    new URL(link);
    return true;
  } catch {
    // 如果不是完整URL，检查是否包含关键字
    return /douyin|kuaishou|xiaohongshu|dy\.com|ks\.com|v\.kuaishou\.com|kwai\.app|ks\.app|gifshow\.com|xhslink\.com/i.test(link);
  }
}

// 清理和标准化链接
function cleanLink(link) {
  // 移除多余的空格和换行
  link = link.trim().replace(/\s+/g, ' ');
  
  // 提取URL部分
  const urlMatch = link.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  // 如果没有找到完整URL，尝试提取域名相关部分
  const domainMatch = link.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/);
  if (domainMatch) {
    return 'https://' + domainMatch[1];
  }
  
  return link;
}
