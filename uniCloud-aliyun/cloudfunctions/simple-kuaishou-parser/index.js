'use strict';

/**
 * 快手内容解析器
 * 支持快手视频和图文内容的解析，包括去水印处理
 */

exports.main = async (event, context) => {
  const { link, forceRemoveWatermark = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为快手链接
    if (!isKuaishouLink(link)) {
      return {
        success: false,
        message: '仅支持快手链接'
      };
    }

    // 解析快手内容
    const result = await parseKuaishouContent(link);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      version: "PC端新版本"
    };

  } catch (error) {
    console.error('快手解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: undefined
    };
  }
};

// 检测是否为快手链接
function isKuaishouLink(url) {
  const patterns = [
    /kuaishou\.com/i,
    /kwai\.app/i,
    /ks\.app/i,
    /gifshow\.com/i
  ];
  return patterns.some(pattern => pattern.test(url));
}

// 生成随机指纹信息
function generateRandomFingerprint() {
  const ip = `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  
  return {
    ip: ip,
    sessionId: Math.random().toString(36).substring(2, 15),
    deviceId: Math.random().toString(36).substring(2, 15)
  };
}

// 生成移动端Cookie
function generateMobileCookies() {
  // 使用移动端适配的Cookie
  return 'kpn=KUAISHOU; kpf=MOBILE_WEB; language=zh-cn';
}

// 使用移动端请求头（参考抖音解析器的成功策略）
const REAL_BROWSER_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
};

// 获取重定向后的真实URL (简化版本，模仿PHP)
async function getRealUrl(shareUrl) {
  try {
    console.log('获取快手真实URL:', shareUrl);
    
    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false, // 先不跟随重定向，获取重定向地址
      timeout: 15000,
      headers: REAL_BROWSER_HEADERS
    });

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;
    
    console.log('获取快手真实URL:', realUrl);
    
    return realUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error);
    return shareUrl;
  }
}

// 从URL中提取内容ID和类型
function extractContentInfo(url) {
  try {
    // 匹配视频链接: /short-video/{id}
    const videoMatch = url.match(/short-video\/([^?&]+)/);
    if (videoMatch) {
      console.log(`✓ 提取到内容ID: ${videoMatch[1]} (视频)`);
      return {
        id: videoMatch[1],
        type: 'video',
        requestUrl: url
      };
    }

    // 匹配图文链接: /photo/{id}
    const photoMatch = url.match(/photo\/([^?&]+)/);
    if (photoMatch) {
      const id = photoMatch[1];
      console.log(`✓ 提取到内容ID: ${id} (图文)`);
      return {
        id: id,
        type: 'photo',
        requestUrl: `https://www.kuaishou.com/short-video/${id}` // 图文也用video接口
      };
    }

    // 从重定向URL中提取ID
    const mobilePhotoMatch = url.match(/photoId=([^&]+)/);
    if (mobilePhotoMatch) {
      const id = mobilePhotoMatch[1];
      console.log(`✓ 从参数提取内容ID: ${id}`);
      return {
        id: id,
        type: 'photo', 
        requestUrl: `https://www.kuaishou.com/short-video/${id}`
      };
    }

    // 从fw/photo路径提取ID
    const fwPhotoMatch = url.match(/fw\/photo\/([^?&]+)/);
    if (fwPhotoMatch) {
      const id = fwPhotoMatch[1];
      console.log(`✓ 从路径提取内容ID: ${id}`);
      return {
        id: id,
        type: 'photo',
        requestUrl: `https://www.kuaishou.com/short-video/${id}`
      };
    }

    // 处理短链接格式 (v.kuaishou.com, v.m.chenzhongtech.com等)
    if (url.match(/v\.kuaishou\.com|v\.m\.chenzhongtech\.com|v\.m\.kuaishou\.com/)) {
      console.log('检测到短链接，需要重定向获取内容ID');
      return {
        id: 'pending', // 标记为待获取
        type: 'video', // 默认假设为视频
        requestUrl: url
      };
    }
    
    console.log('⚠️ 未能从URL中提取内容ID');
    return {
      id: null,
      type: 'unknown',
      requestUrl: url
    };
  } catch (error) {
    console.error('提取内容信息失败:', error);
    return {
      id: null,
      type: 'unknown',
      requestUrl: url
    };
  }
}

// 获取页面HTML内容 (简化版本，模仿PHP的策略)
async function getPageContent(url) {
  try {
    console.log('获取快手页面内容:', url);
    
    // 模仿PHP版本的简单请求策略
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      followRedirect: true, // 允许自动跟随重定向，模仿PHP的CURLOPT_FOLLOWLOCATION
      timeout: 15000,
      dataType: 'text',
      headers: REAL_BROWSER_HEADERS
    });

    let htmlContent = response.data;
    if (typeof htmlContent !== 'string') {
      htmlContent = htmlContent?.toString() || '';
      if (!htmlContent) {
        throw new Error('页面内容格式错误');
      }
    }

    // 保存HTML到test目录用于调试
    try {
      const fs = require('fs');
      const path = require('path');
      
      // 创建test目录路径
      const testDir = path.join(__dirname, '..', '..', '..', 'pages', 'test');
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }
      
      // 生成带时间戳的文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
      const htmlFileName = `kuaishou_raw_${timestamp}.html`;
      const htmlFilePath = path.join(testDir, htmlFileName);
      
      fs.writeFileSync(htmlFilePath, htmlContent, 'utf8');
      console.log('HTML内容已保存到:', htmlFileName);
      console.log('HTML内容长度:', htmlContent.length);
      console.log('HTML前500字符:', htmlContent.substring(0, 500));
    } catch (writeError) {
      console.error('保存HTML文件失败:', writeError);
    }

    return htmlContent;
  } catch (error) {
    console.error('获取页面内容失败:', error);
    throw new Error('无法获取页面内容: ' + error.message);
  }
}

// 从页面中提取视频数据
function extractVideoData(html, contentId) {
  try {
    console.log('开始提取视频数据，内容ID:', contentId);
    
    // 匹配 window.__APOLLO_STATE__ 数据
    const apolloStateMatch = html.match(/window\.__APOLLO_STATE__\s*=\s*({[\s\S]*?})<\/script>/);
    if (!apolloStateMatch) {
      console.log('未找到 __APOLLO_STATE__ 数据');
      console.log('页面内容前1000字符:', html.substring(0, 1000));
      
      // 尝试其他可能的数据源
      const initialStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({[\s\S]*?})<\/script>/);
      if (initialStateMatch) {
        console.log('找到 __INITIAL_STATE__ 数据，尝试解析');
        return extractFromInitialState(initialStateMatch[1], contentId);
      }
      
      return null;
    }

    let jsonData = apolloStateMatch[1];
    
    // 清理无效的函数定义
    const functionPattern = /function\s*\([^)]*\)\s*{[^}]*}/g;
    jsonData = jsonData.replace(functionPattern, '{}');
    
    // 清理末尾的分号和多余的逗号
    jsonData = jsonData.replace(/,\s*(?=}|])/g, '');
    const charChainToRemove = ';({});';
    jsonData = jsonData.replace(charChainToRemove, '');

    try {
      const decoded = JSON.parse(jsonData);
      
      // 查找视频详情数据
      const videoDetailKey = `VisionVideoDetailPhoto:${contentId}`;
      const videoInfo = decoded?.defaultClient?.[videoDetailKey];
      
      if (videoInfo) {
        console.log('找到视频数据:', videoDetailKey);
        
        return {
          title: videoInfo.caption || '',
          author: videoInfo.userName || '',
          cover: videoInfo.coverUrl || '',
          videoUrl: videoInfo.photoUrl || '',
          type: 'video'
        };
      }
      
      console.log('未在 defaultClient 中找到视频数据，键名:', videoDetailKey);
      return null;
      
    } catch (parseError) {
      console.error('解析 APOLLO_STATE JSON 失败:', parseError);
      return null;
    }

  } catch (error) {
    console.error('提取视频数据失败:', error);
    return null;
  }
}

// 从 __INITIAL_STATE__ 中提取数据
function extractFromInitialState(jsonString, contentId) {
  try {
    console.log('尝试从 __INITIAL_STATE__ 解析数据');
    
    // 处理 undefined
    jsonString = jsonString.replace(/undefined/g, 'null');
    
    const data = JSON.parse(jsonString);
    console.log('__INITIAL_STATE__ 解析成功，查找内容ID:', contentId);
    
    // 遍历查找包含内容的数据
    for (const key in data) {
      if (data[key] && typeof data[key] === 'object') {
        const item = data[key];
        
        // 检查是否包含视频信息
        if (item.photo && item.photo.caption) {
          console.log('找到内容数据:', key);
          
          return {
            title: item.photo.caption || '',
            author: item.photo.user?.name || item.photo.userName || '',
            cover: item.photo.coverUrl || '',
            videoUrl: item.photo.photoUrl || '',
            type: 'video'
          };
        }
      }
    }
    
    console.log('在 __INITIAL_STATE__ 中未找到有效内容');
    return null;
    
  } catch (error) {
    console.error('解析 __INITIAL_STATE__ 失败:', error);
    return null;
  }
}

// 从页面中提取图文数据  
function extractPhotoData(html) {
  try {
    // 尝试多种数据源匹配
    let initStateMatch = html.match(/window\.INIT_STATE\s*=\s*({[\s\S]*?})<\/script>/);
    
    if (!initStateMatch) {
      // 尝试匹配其他可能的数据结构
      initStateMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({[\s\S]*?})<\/script>/);
    }
    
    if (!initStateMatch) {
      // 尝试匹配 APOLLO_STATE (图文也可能使用这个)
      initStateMatch = html.match(/window\.__APOLLO_STATE__\s*=\s*({[\s\S]*?})<\/script>/);
    }
    
    if (!initStateMatch) {
      console.log('未找到任何数据源 (INIT_STATE, __INITIAL_STATE__, __APOLLO_STATE__)');
      console.log('页面内容前500字符:', html.substring(0, 500));
      return null;
    }

    let jsonString = initStateMatch[1];
    
    // 清理转义字符
    jsonString = jsonString.replace(/\\/g, '');
    
    try {
      const data = JSON.parse(jsonString);
      
      // 查找图文数据
      let photoData = null;
      for (const key in data) {
        if (key.startsWith('tusjoh') && data[key]?.fid) {
          photoData = data[key];
          break;
        }
      }
      
      if (!photoData) {
        console.log('未找到图文数据');
        return null;
      }

      const photo = photoData.photo;
      if (!photo) {
        console.log('图文数据结构异常');
        return null;
      }

      // 提取图片列表
      let images = [];
      const imageList = photo.ext_params?.atlas?.list || [photo.coverUrls?.[0]?.url];
      
      if (Array.isArray(imageList)) {
        images = imageList.map(img => {
          if (typeof img === 'string') {
            return img;
          } else {
            // 如果是CDN路径，添加前缀
            return `http://tx2.a.yximgs.com/${img}`;
          }
        }).filter(url => url);
      }

      // 提取音乐
      const musicUrl = photo.ext_params?.atlas?.music || photo.music?.audioUrls?.[0]?.url;
      const music = musicUrl ? `http://txmov2.a.kwimgs.com${musicUrl}` : '';

      return {
        title: photo.caption || '',
        author: photo.userName || '',
        images: images,
        music: music,
        count: images.length,
        type: 'photo'
      };

    } catch (parseError) {
      console.error('解析 INIT_STATE JSON 失败:', parseError);
      return null;
    }

  } catch (error) {
    console.error('提取图文数据失败:', error);
    return null;
  }
}

// 解析快手内容 (模仿PHP版本策略)
async function parseKuaishouContent(shareUrl) {
  try {
    console.log('开始解析快手内容:', shareUrl);
    
    // 获取真实URL
    const realUrl = await getRealUrl(shareUrl);
    console.log('获取到真实URL:', realUrl);
    
    // 提取内容信息
    const contentInfo = extractContentInfo(realUrl);
    console.log('内容信息:', contentInfo);
    
    if (!contentInfo.id || contentInfo.id === null) {
      throw new Error('无法提取内容ID，URL: ' + realUrl);
    }
    
    // 如果是短链接，需要先从重定向URL中提取真实ID
    if (contentInfo.id === 'pending') {
      console.log('短链接需要从重定向URL中提取内容ID...');
      const redirectedInfo = extractContentInfo(realUrl);
      if (redirectedInfo.id && redirectedInfo.id !== 'pending') {
        contentInfo.id = redirectedInfo.id;
        contentInfo.type = redirectedInfo.type;
        console.log('从重定向URL提取到内容ID:', contentInfo.id, '类型:', contentInfo.type);
      } else {
        throw new Error('短链接重定向后仍无法提取内容ID，URL: ' + realUrl);
      }
    }

    // 生成完整的Cookie（模仿浏览器）
    const generateCookies = () => {
      const did = `web_${Math.random().toString(36).substring(2)}${Date.now()}`;
      return [
        `did=${did}`,
        'clientid=3',
        'kpf=PC_WEB',
        'kpn=KUAISHOU_VISION',
        `didv=${Date.now()}`,
        'kwfv1=PnGU+9+Y8008S+nH0U+0mjPf8fP08f+98f+nLlwnrIP9P9G98YPf8jPBQSweS0+nr9G0mD8B+fP/L98/qlPe4f8e4YweHIw/GEwePE8/ZMP0P98ecF+f+DGnLh+nQ08BcIPBHEP98f+0r7P0ql+AmjG0GlG0D98nQ0G9rh+AWF+/PMGnPEGfPU+0HlG9+j8eGl+/LEweqE8eQ0P/D98eYS+c=='
      ].join('; ');
    };

    // 构造带完整参数的最终URL
    const buildFinalUrl = (baseUrl, realUrl) => {
      try {
        const realUrlObj = new URL(realUrl);
        const params = realUrlObj.search;
        
        // 如果realUrl有参数，则添加到baseUrl
        if (params && baseUrl.indexOf('?') === -1) {
          return baseUrl + params + '&utm_source=app_share&utm_medium=app_share&utm_campaign=app_share&location=app_share';
        }
        return baseUrl;
      } catch (error) {
        return baseUrl;
      }
    };

    // 使用移动端策略，PC端被反爬虫拦截
    let strategies = [
      {
        name: '移动端Safari访问',
        headers: {
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        },
        urls: [
          buildFinalUrl(`https://www.kuaishou.com/short-video/${contentInfo.id}`, realUrl),
          buildFinalUrl(`https://www.kuaishou.com/photo/${contentInfo.id}`, realUrl)
        ]
      }
    ];
    
    console.log('使用移动端策略获取页面内容');

    // 清理上次生成的JSON文件
    try {
      const fs = require('fs');
      const path = require('path');
      const testDir = path.join(__dirname, '..', '..', '..', 'pages', 'test');
      
      if (fs.existsSync(testDir)) {
        const files = fs.readdirSync(testDir);
        const kuaishouJsonFiles = files.filter(file => 
          file.startsWith('kuaishou_extracted_') && file.endsWith('.json')
        );
        
        kuaishouJsonFiles.forEach(file => {
          const filePath = path.join(testDir, file);
          fs.unlinkSync(filePath);
          console.log(`清理上次的JSON文件: ${file}`);
        });
      }
    } catch (cleanError) {
      console.log('清理文件时出错:', cleanError.message);
    }

    // 使用移动端Safari策略
    const strategy = strategies[0]; // 只有一个移动端策略
    let pageContent = null;
    let successUrl = null;
    let successStrategy = null;

    console.log(`使用策略: ${strategy.name}`);
    
    for (const url of strategy.urls) {
      try {
        console.log(`  访问:`, url);
        
        // 请求内容
        const response = await uniCloud.httpclient.request(url, {
          method: 'GET',
          followRedirect: true,
          timeout: 15000,
          dataType: 'text',
          headers: strategy.headers
        });

        let htmlContent = response.data;
        if (typeof htmlContent !== 'string') {
          htmlContent = htmlContent?.toString() || '';
        }
        
        // 检查页面内容质量
        const hasServerError = htmlContent.includes('internal server error') &&
                               htmlContent.includes('"result":1000') && 
                               htmlContent.includes('error_msg');
        
        const isLongContent = htmlContent.length > 1000;
        const hasHTML = htmlContent.includes('<html') || htmlContent.includes('<!DOCTYPE');
        const hasInitState = htmlContent.includes('window.INIT_STATE');
        
        // 检查内容是否有效
        const isValidContent = htmlContent && !hasServerError && 
                              (isLongContent || hasHTML || hasInitState);
        
        if (isValidContent) {
          console.log(`  ✅ 成功获取有效内容! 内容长度: ${htmlContent.length}`);
          pageContent = htmlContent;
          successUrl = url;
          successStrategy = strategy.name;
          break;
        } else {
          console.log(`  ❌ 内容无效，长度: ${htmlContent.length}`);
        }
      } catch (error) {
        console.log(`  ❌ 请求失败:`, error.message);
        continue;
      }
    }
    
    // 检查是否获取到有效内容
    if (!pageContent || !successUrl) {
      throw new Error('无法获取到有效页面内容');
    }

    // 专门提取window.INIT_STATE的JSON数据
    console.log('HTML获取成功，开始提取window.INIT_STATE...');
    console.log('HTML长度:', pageContent.length);
    
    let extractedData = null;
    let dataSource = null;
    
    // 提取window.INIT_STATE
    if (pageContent.includes('window.INIT_STATE')) {
      console.log('✓ 找到window.INIT_STATE');
      try {
        // 使用更强大的正则提取window.INIT_STATE的JSON数据
        const initStateMatch = pageContent.match(/window\.INIT_STATE\s*=\s*({.+?})\s*<\/script>/s);
        if (initStateMatch) {
          const jsonStr = initStateMatch[1];
          extractedData = JSON.parse(jsonStr);
          dataSource = 'window.INIT_STATE';
          console.log('✓ 成功解析window.INIT_STATE JSON');
        } else {
          console.log('❌ 找到window.INIT_STATE但无法提取JSON');
        }
      } catch (error) {
        console.log('❌ window.INIT_STATE JSON解析失败:', error.message);
      }
    }
    
    // 如果没找到INIT_STATE，尝试其他数据源
    if (!extractedData) {
      const dataSources = [
        { pattern: /window\.__INITIAL_STATE__\s*=\s*({.*?});/s, name: 'window.__INITIAL_STATE__' },
        { pattern: /window\.__APOLLO_STATE__\s*=\s*({.*?});/s, name: 'window.__APOLLO_STATE__' },
        { pattern: /"pageProps":\s*({.*?}),/s, name: 'pageProps' }
      ];
      
      for (const source of dataSources) {
        if (pageContent.includes(source.name)) {
          console.log(`✓ 尝试提取 ${source.name}`);
          try {
            const match = pageContent.match(source.pattern);
            if (match) {
              extractedData = JSON.parse(match[1]);
              dataSource = source.name;
              console.log(`✓ 成功解析 ${source.name} JSON`);
              break;
            }
          } catch (error) {
            console.log(`❌ ${source.name} JSON解析失败:`, error.message);
          }
        }
      }
    }
    
    // 保存提取的JSON数据到文件
    if (extractedData) {
      try {
        const fs = require('fs');
        const path = require('path');
        
        const testDir = path.join(__dirname, '..', '..', '..', 'pages', 'test');
        if (!fs.existsSync(testDir)) {
          fs.mkdirSync(testDir, { recursive: true });
        }
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
        const strategyName = successStrategy ? successStrategy.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') : '未知策略';
        const jsonFileName = `kuaishou_extracted_${dataSource.replace(/[^a-zA-Z0-9]/g, '_')}_from_${strategyName}_${timestamp}.json`;
        const jsonFilePath = path.join(testDir, jsonFileName);
        
        fs.writeFileSync(jsonFilePath, JSON.stringify(extractedData, null, 2), 'utf8');
        console.log(`✅ 提取的JSON数据已保存到: ${jsonFileName}`);
        console.log(`数据源: ${dataSource}`);
        console.log(`JSON大小: ${JSON.stringify(extractedData).length} 字符`);
        
        // 分析JSON结构
        console.log('JSON结构分析:');
        console.log('- 顶级键:', Object.keys(extractedData));
        
      } catch (saveError) {
        console.log('保存JSON文件失败:', saveError.message);
      }
    } else {
      console.log('❌ 未找到任何可提取的JSON数据');
    }
    
    // 解析快手视频数据
    let parsedVideoData = null;
    if (extractedData) {
      parsedVideoData = parseKuaishouVideoData(extractedData);
      console.log('解析的视频数据:', parsedVideoData);
    }
    
    // 返回解析结果
    if (parsedVideoData) {
      return {
        title: parsedVideoData.title,
        author: parsedVideoData.author,
        content: parsedVideoData.content,
        videoUrl: parsedVideoData.videoUrl,
        coverUrl: parsedVideoData.coverUrl,
        userAvatar: parsedVideoData.userAvatar,
        duration: parsedVideoData.duration,
        viewCount: parsedVideoData.viewCount,
        likeCount: parsedVideoData.likeCount,
        commentCount: parsedVideoData.commentCount,
        type: 'video',
        platform: 'kuaishou',
        source: '快手',
        originalUrl: shareUrl,
        processedData: {
          data: extractedData,
          dataSource: dataSource,
          parsedData: parsedVideoData,
          type: 'video_parsed',
          htmlLength: pageContent.length,
          jsonSize: extractedData ? JSON.stringify(extractedData).length : 0
        },
        debug: {
          realUrl: realUrl,
          contentType: contentInfo.type,
          contentId: contentInfo.id,
          requestUrl: successUrl,
          successStrategy: successStrategy,
          dataSource: dataSource,
          extractSuccess: !!extractedData,
          parseSuccess: !!parsedVideoData
        }
      };
    } else {
      return {
        title: extractedData ? 'JSON提取成功但解析失败' : 'JSON提取失败',
        author: '数据提取',
        content: extractedData ? `成功从${dataSource}提取JSON数据，但未能解析视频信息` : '未找到有效的JSON数据',
        processedData: {
          data: extractedData,
          dataSource: dataSource,
          type: 'json_extracted',
          isUrl: false,
          htmlLength: pageContent.length,
          jsonSize: extractedData ? JSON.stringify(extractedData).length : 0
        },
        type: 'json_extracted',
        platform: 'kuaishou',
        source: '快手',
        coverUrl: null,
        originalUrl: shareUrl,
        debug: {
          realUrl: realUrl,
          contentType: contentInfo.type,
          contentId: contentInfo.id,
          requestUrl: successUrl,
          successStrategy: successStrategy,
          dataSource: dataSource,
          extractSuccess: !!extractedData,
          parseSuccess: false
        }
      };
    }

  } catch (error) {
    console.error('快手解析失败:', error);
    throw error;
  }
}

// 解析快手视频数据
function parseKuaishouVideoData(initStateData) {
  try {
    console.log('开始解析快手视频数据...');
    
    // 递归搜索函数
    function findVideoData(obj, depth = 0) {
      if (depth > 3) return null; // 限制搜索深度防止无限递归
      
      for (const key in obj) {
        const item = obj[key];
        
        // 检查当前对象是否包含视频数据
        if (item && typeof item === 'object' && 
            item.caption && item.userName && item.mainMvUrls && item.coverUrls) {
          console.log(`找到视频数据，深度: ${depth}, 路径键: ${key.substring(0, 50)}...`);
          return item;
        }
        
        // 递归搜索子对象
        if (item && typeof item === 'object') {
          const result = findVideoData(item, depth + 1);
          if (result) return result;
        }
      }
      return null;
    }
    
    // 递归查找所有质量版本的视频URL
    function findAllVideoUrls(obj, depth = 0) {
      const videoUrls = [];
      if (depth > 6) return videoUrls; // 限制递归深度
      
      for (const key in obj) {
        const item = obj[key];
        if (item && typeof item === 'object') {
          // 检查是否是视频URL对象
          if (item.url && typeof item.url === 'string' && item.url.includes('.mp4')) {
            const quality = getVideoQualityFromUrl(item.url);
            const cdn = extractCdnFromUrl(item.url);
            videoUrls.push({
              url: item.url,
              cdn: cdn,
              quality: quality,
              source: 'direct'
            });
            console.log(`发现视频URL: ${quality} (${cdn})`);
          }
          
          // 继续递归查找
          const subUrls = findAllVideoUrls(item, depth + 1);
          videoUrls.push(...subUrls);
        }
      }
      return videoUrls;
    }
    
    // 开始递归搜索
    const videoItem = findVideoData(initStateData);
    
    if (videoItem) {
        
        console.log('找到视频数据，开始解析...');
        
        // 提取基本信息
        const title = videoItem.caption || '';
        const author = videoItem.userName || '';
        const duration = videoItem.duration || 0;
        const photoType = videoItem.photoType || '';
        
        // 检测内容类型
        const contentType = getKuaishouContentType(videoItem);
        console.log(`📋 内容类型: ${contentType.description} (${contentType.type})`);
        
        // 根据内容类型提取对应的媒体资源
        let videoUrls = [];
        let imageUrls = [];
        
        if (contentType.type === 'video') {
            // 纯视频：查找所有质量的视频URL
            console.log('开始查找所有质量的视频URL...');
            let allVideoUrls = findAllVideoUrls(initStateData);
            
            // 从mainMvUrls中添加标准质量视频URL
            if (videoItem.mainMvUrls && videoItem.mainMvUrls.length > 0) {
              const mainUrls = videoItem.mainMvUrls.map(mv => ({
                url: mv.url || '',
                cdn: mv.cdn || '',
                quality: getVideoQualityFromUrl(mv.url || ''),
                source: 'mainMvUrls'
              }));
              videoUrls.push(...mainUrls);
            }
            
            // 合并所有视频URL并去重
            const allUrls = [...videoUrls, ...allVideoUrls];
            const uniqueUrls = [];
            const seenUrls = new Set();
            
            for (const urlInfo of allUrls) {
              const urlKey = urlInfo.url.split('?')[0]; // 去除参数部分进行去重
              if (!seenUrls.has(urlKey)) {
                seenUrls.add(urlKey);
                uniqueUrls.push(urlInfo);
              }
            }
            
            // 按质量排序：UltraV5 > HighV5 > Standard
            uniqueUrls.sort((a, b) => {
              const qualityOrder = { 
                'UltraV5 (4K)': 4, 
                'UltraV5': 3, 
                'HighV5 (1080p)': 2, 
                'HighV5': 2, 
                'Standard (720p)': 1, 
                'Standard': 1 
              };
              const aScore = qualityOrder[a.quality] || 0;
              const bScore = qualityOrder[b.quality] || 0;
              return bScore - aScore;
            });
            
            videoUrls = uniqueUrls;
            console.log(`找到 ${videoUrls.length} 个视频URL，质量分布:`, 
                       videoUrls.map(v => `${v.quality}(${v.cdn})`));
                       
        } else if (contentType.type === 'single_image' || contentType.type === 'image_text') {
            // 单张图片或图集：提取图片URL
            console.log('开始提取图片URL...');
            imageUrls = extractImageUrls(videoItem, initStateData);
            
            // 检查是否是Live Photo，如果是则尝试提取背景视频
            if (contentType.type === 'image_text' && 
                videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.music) {
                console.log('🎬 检测到可能的Live Photo，寻找背景视频...');
                const livePhotoVideos = findLivePhotoVideos(initStateData);
                if (livePhotoVideos.length > 0) {
                    console.log(`找到 ${livePhotoVideos.length} 个Live Photo背景视频`);
                    videoUrls.push(...livePhotoVideos);
                }
            }
            
            // 去重图片URL
            const uniqueImageUrls = [];
            const seenImageUrls = new Set();
            
            for (const imgInfo of imageUrls) {
              const urlKey = imgInfo.url.split('?')[0];
              if (!seenImageUrls.has(urlKey)) {
                seenImageUrls.add(urlKey);
                uniqueImageUrls.push(imgInfo);
              }
            }
            
            imageUrls = uniqueImageUrls;
            console.log(`找到 ${imageUrls.length} 张图片，格式分布:`, 
                       imageUrls.map(img => `${img.format}(${img.cdn})`));
        }
        
        // 提取封面图URL（收集所有CDN）
        let coverUrl = '';
        let coverUrls = [];
        if (videoItem.coverUrls && videoItem.coverUrls.length > 0) {
          coverUrls = videoItem.coverUrls.map(cover => ({
            url: cover.url || '',
            cdn: cover.cdn || ''
          }));
          coverUrl = coverUrls[0]?.url || '';
        }
        
        // 提取用户头像URL（收集所有CDN）
        let userAvatar = '';
        let headUrls = [];
        if (videoItem.headUrls && videoItem.headUrls.length > 0) {
          headUrls = videoItem.headUrls.map(head => ({
            url: head.url || '',
            cdn: head.cdn || ''
          }));
          userAvatar = headUrls[0]?.url || '';
        }
        
        // 提取统计数据
        const viewCount = videoItem.viewCount || 0;
        const likeCount = videoItem.likeCount || 0;
        const commentCount = videoItem.commentCount || 0;
        const shareCount = videoItem.shareCount || 0;
        
        // 保持原始标题，不进行清理
        const originalTitle = title;
        
        // 提取标签
        const tags = [];
        const tagMatches = title.match(/#[^\s#]*/g);
        if (tagMatches) {
          tags.push(...tagMatches.map(tag => tag.replace('#', '')));
        }
        
        // 格式化打印结果
        console.log(`\n=== 🎬 快手${contentType.description}解析成功 ===`);
        console.log(`📝 标题: ${originalTitle}`);
        console.log(`👤 作者: ${author}`);
        if (duration > 0) {
          console.log(`⏱️  时长: ${Math.floor(duration / 1000)}秒`);
        }
        console.log(`👀 播放量: ${viewCount.toLocaleString()}`);
        console.log(`👍 点赞量: ${likeCount.toLocaleString()}`);
        console.log(`💬 评论量: ${commentCount.toLocaleString()}`);
        
        // 显示视频链接（仅纯视频）
        if (contentType.type === 'video' && videoUrls.length > 0) {
          console.log(`\n🎬 视频链接 (共${videoUrls.length}个):`);
          videoUrls.forEach((v, index) => {
            let qualityDesc = '';
            switch(v.quality) {
              case 'UltraV5 (4K)': 
              case 'UltraV5': 
                qualityDesc = '超清 4K'; break;
              case 'HighV5 (1080p)':
              case 'HighV5': 
                qualityDesc = '高清 1080p'; break;
              case 'Standard (720p)':
              case 'Standard': 
                qualityDesc = '标清 720p'; break;
              default: qualityDesc = v.quality || '标准'; break;
            }
            console.log(`  ${index + 1}. [${qualityDesc}] (CDN: ${v.cdn})`);
            console.log(`     ${v.url}`);
            console.log('');
          });
        }
        
        // 显示图片链接（单张图片和图集）
        if ((contentType.type === 'single_image' || contentType.type === 'image_text') && imageUrls.length > 0) {
          const imageTypeDesc = contentType.type === 'single_image' ? '图片' : '图集';
          const hasLivePhotoVideo = videoUrls.some(v => v.type === 'live_photo_background');
          const typeDescWithLive = hasLivePhotoVideo ? `${imageTypeDesc}(Live Photo)` : imageTypeDesc;
          
          console.log(`\n🖼️ ${typeDescWithLive}链接 (共${imageUrls.length}张):`);
          
          // 按图片索引分组显示
          const imageGroups = {};
          imageUrls.forEach(img => {
            if (!imageGroups[img.index]) {
              imageGroups[img.index] = [];
            }
            imageGroups[img.index].push(img);
          });
          
          Object.keys(imageGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(index => {
            const images = imageGroups[index];
            console.log(`  图片 ${parseInt(index) + 1}:`);
            images.forEach((img, i) => {
              const formatDesc = img.format.toUpperCase();
              console.log(`    ${i + 1}. [${formatDesc}] (CDN: ${img.cdn})`);
              console.log(`       ${img.url}`);
            });
            console.log('');
          });
          
          // 显示Live Photo背景视频（如果有）
          if (hasLivePhotoVideo) {
            const livePhotoVideos = videoUrls.filter(v => v.type === 'live_photo_background');
            console.log(`🎬 Live Photo背景视频 (共${livePhotoVideos.length}个):`);
            livePhotoVideos.forEach((v, index) => {
              console.log(`  ${index + 1}. [${v.quality}] (CDN: ${v.cdn})`);
              console.log(`     ${v.url}`);
              console.log('');
            });
          }
        }
        console.log(`🖼️ 封面图 (共${coverUrls.length}个CDN):`);
        if (coverUrls && coverUrls.length > 0) {
          coverUrls.forEach((cover, index) => {
            console.log(`  ${index + 1}. (CDN: ${cover.cdn})`);
            console.log(`     ${cover.url}`);
            console.log('');
          });
        }
        console.log(`👑 用户头像 (共${headUrls.length}个CDN):`);
        if (headUrls && headUrls.length > 0) {
          headUrls.forEach((head, index) => {
            console.log(`  ${index + 1}. (CDN: ${head.cdn})`);
            console.log(`     ${head.url}`);
            console.log('');
          });
        }
        if (tags && tags.length > 0) {
          console.log('\n🏷️ 标签:');
          tags.forEach(tag => console.log(`  #${tag}`));
        }
        console.log('===============================\n');
        
        // 根据内容类型设置主要URL
        const videoUrl = videoUrls.length > 0 ? videoUrls[0].url : '';
        const imageUrl = imageUrls.length > 0 ? imageUrls[0].url : '';
        
        return {
          title: originalTitle, // 保持原始标题（包含标签）
          content: originalTitle, // 保留原始内容
          author: author,
          videoUrl: videoUrl,
          imageUrl: imageUrl, // 新增图片URL字段
          coverUrl: coverUrl,
          userAvatar: userAvatar,
          duration: Math.floor(duration / 1000), // 转换为秒
          viewCount: viewCount,
          likeCount: likeCount,
          commentCount: commentCount,
          shareCount: shareCount,
          tags: tags,
          videoUrls: videoUrls, // 包含所有质量版本
          imageUrls: imageUrls, // 新增图片URL数组
          contentType: contentType, // 新增内容类型信息
          userId: videoItem.userId || '',
          timestamp: videoItem.timestamp || 0,
          videoId: videoItem.videoId || ''
        };
    }
    
    console.log('未找到匹配的视频数据结构');
    return null;
    
  } catch (error) {
    console.error('解析快手视频数据失败:', error);
    return null;
  }
}

// 判断视频质量
function getVideoQuality(url) {
  if (url.includes('_v6UltraV5.mp4')) {
    return 'UltraV5';
  } else if (url.includes('_v6HighV5.mp4')) {
    return 'HighV5';
  } else {
    return 'Standard';
  }
}

// 从URL中提取CDN信息
function extractCdnFromUrl(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    return 'unknown';
  }
}

// 改进的视频质量检测函数，支持更多URL格式
function getVideoQualityFromUrl(url) {
  if (url.includes('_v6UltraV5.mp4') || url.includes('tt=v6UltraV5')) {
    return 'UltraV5 (4K)';
  } else if (url.includes('_v6HighV5.mp4') || url.includes('tt=v6HighV5')) {
    return 'HighV5 (1080p)';
  } else if (url.includes('tt=b') || url.includes('_b_B')) {
    return 'Standard (720p)';
  } else {
    return 'Standard (720p)';
  }
}

// 检测快手内容类型
function getKuaishouContentType(videoItem) {
  switch (videoItem.photoType) {
    case "VIDEO":
      return {
        type: "video",
        description: "纯视频",
        hasVideo: true,
        hasImages: false
      };
      
    case "HORIZONTAL_ATLAS":
      return {
        type: "image_text", 
        description: "图文视频/图集",
        hasVideo: false,
        hasImages: true
      };
      
    case "SINGLE_PICTURE":
      return {
        type: "single_image",
        description: "单张图片",
        hasVideo: false, 
        hasImages: true
      };
      
    default:
      return {
        type: "unknown",
        description: "未知类型",
        photoType: videoItem.photoType || "undefined"
      };
  }
}

// 提取图片URL (支持单张图片和图集)
function extractImageUrls(videoItem, initStateData) {
  const imageUrls = [];
  const contentType = getKuaishouContentType(videoItem);
  
  if (contentType.type === 'single_image') {
    // 单张图片：从coverUrls或其他图片字段提取
    if (videoItem.coverUrls && videoItem.coverUrls.length > 0) {
      videoItem.coverUrls.forEach((cover, index) => {
        imageUrls.push({
          url: cover.url || '',
          cdn: cover.cdn || '',
          index: index,
          format: getImageFormat(cover.url || ''),
          type: 'cover'
        });
      });
    }
    
    // 也检查是否有atlas结构（有些单图也可能在atlas中）
    if (videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.list) {
      videoItem.ext_params.atlas.list.forEach((imagePath, index) => {
        const cdnList = videoItem.ext_params.atlas.cdnList || [];
        cdnList.forEach(cdnInfo => {
          const fullUrl = `https://${cdnInfo.cdn}${imagePath}`;
          imageUrls.push({
            url: fullUrl,
            cdn: cdnInfo.cdn,
            index: index,
            format: getImageFormat(imagePath),
            type: 'atlas'
          });
        });
      });
    }
    
  } else if (contentType.type === 'image_text') {
    // 图文视频/图集：从atlas结构提取所有图片
    
    // 检查是否是Live Photo（动图），如果是则提取背景视频
    if (videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.music) {
      console.log('🎬 检测到Live Photo，寻找背景视频...');
      // Live Photo通常有背景音乐和可能的背景视频
    }
    
    // 从ext_params.atlas提取（WebP格式）
    if (videoItem.ext_params && videoItem.ext_params.atlas && videoItem.ext_params.atlas.list) {
      const cdnList = videoItem.ext_params.atlas.cdnList || [];
      videoItem.ext_params.atlas.list.forEach((imagePath, index) => {
        cdnList.forEach(cdnInfo => {
          const fullUrl = `https://${cdnInfo.cdn}${imagePath}`;
          imageUrls.push({
            url: fullUrl,
            cdn: cdnInfo.cdn,
            index: index,
            format: getImageFormat(imagePath),
            type: 'atlas_webp'
          });
        });
      });
    }
    
    // 从顶层atlas提取（JPG格式）
    const atlasData = findAtlasInData(initStateData);
    if (atlasData && atlasData.list) {
      const cdnList = atlasData.cdnList || atlasData.cdn || [];
      atlasData.list.forEach((imagePath, index) => {
        if (Array.isArray(cdnList)) {
          cdnList.forEach(cdnItem => {
            // 处理CDN可能是字符串或对象的情况
            const cdnName = typeof cdnItem === 'string' ? cdnItem : (cdnItem.cdn || cdnItem.name || 'unknown');
            const fullUrl = `https://${cdnName}${imagePath}`;
            imageUrls.push({
              url: fullUrl,
              cdn: cdnName,
              index: index,
              format: getImageFormat(imagePath),
              type: 'atlas_jpg'
            });
          });
        }
      });
    }
  }
  
  return imageUrls;
}

// 获取图片格式
function getImageFormat(url) {
  if (url.includes('.webp')) return 'webp';
  if (url.includes('.jpg') || url.includes('.jpeg')) return 'jpg';
  if (url.includes('.png')) return 'png';
  return 'unknown';
}

// 在数据中查找atlas结构
function findAtlasInData(data, depth = 0) {
  if (depth > 3) return null;
  
  for (const key in data) {
    const item = data[key];
    if (item && typeof item === 'object') {
      if (key === 'atlas' && item.list && Array.isArray(item.list)) {
        return item;
      }
      
      const result = findAtlasInData(item, depth + 1);
      if (result) return result;
    }
  }
  return null;
}

// 查找Live Photo的背景视频
function findLivePhotoVideos(data, depth = 0) {
  const videoUrls = [];
  if (depth > 6) return videoUrls;
  
  for (const key in data) {
    const item = data[key];
    if (item && typeof item === 'object') {
      // 寻找可能的背景视频标识
      if (item.url && typeof item.url === 'string') {
        // 检查是否是视频文件且可能是背景视频
        if (item.url.includes('.mp4') || item.url.includes('.mov') || item.url.includes('video')) {
          // 排除已经在mainMvUrls中的标准视频
          const isMainVideo = data.mainMvUrls && data.mainMvUrls.some(mv => mv.url === item.url);
          if (!isMainVideo) {
            const quality = getVideoQualityFromUrl(item.url);
            const cdn = extractCdnFromUrl(item.url);
            videoUrls.push({
              url: item.url,
              cdn: cdn,
              quality: quality,
              type: 'live_photo_background',
              source: `depth_${depth}_key_${key}`
            });
            console.log(`发现潜在Live Photo背景视频: ${quality} (${cdn}) [${key}]`);
          }
        }
      }
      
      // 特别检查atlas相关的视频字段
      if (key === 'atlas' && item.video) {
        console.log('发现atlas中的video字段:', typeof item.video);
        if (typeof item.video === 'string' && item.video.includes('.mp4')) {
          const quality = getVideoQualityFromUrl(item.video);
          const cdn = extractCdnFromUrl(item.video);
          videoUrls.push({
            url: item.video,
            cdn: cdn,
            quality: quality,
            type: 'atlas_video'
          });
          console.log(`发现atlas视频: ${quality} (${cdn})`);
        }
      }
      
      // 递归查找
      const subVideos = findLivePhotoVideos(item, depth + 1);
      videoUrls.push(...subVideos);
    }
  }
  return videoUrls;
}