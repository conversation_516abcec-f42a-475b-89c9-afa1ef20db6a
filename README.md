# MarkEraser - 视频去水印工具

一个基于 uni-app 和 uniCloud 开发的视频去水印小程序，支持多个主流平台的链接解析和本地文件处理。

## 功能特性

### 🎯 核心功能
- **链接解析**: 支持抖音、快手、小红书等平台的分享链接解析
- **本地文件**: 支持选择本地图片和视频进行处理
- **历史记录**: 自动保存处理历史，方便回顾和重新处理
- **用户中心**: 个人设置、使用说明、统计信息等

### 📱 界面特色
- **现代化设计**: 采用渐变色彩和圆角设计，界面美观
- **响应式布局**: 适配不同屏幕尺寸
- **交互友好**: 丰富的动画效果和用户反馈

### ⚡ 技术特点
- **云端处理**: 基于 uniCloud 云函数，无需自建服务器
- **多端支持**: 一套代码支持小程序、H5、App
- **本地存储**: 智能的本地数据管理

## 快速开始

### 环境要求
- HBuilderX 3.0+
- uniCloud 阿里云版
- 微信开发者工具（如需发布小程序）

### 安装步骤

1. **导入 HBuilderX**
   - 打开 HBuilderX
   - 文件 -> 导入 -> 从本地目录导入

2. **配置 uniCloud**
   - 右键项目根目录
   - 创建 uniCloud 云开发环境
   - 选择阿里云版本

3. **上传云函数**
   - 右键 `uniCloud-aliyun/cloudfunctions/watermark-remover`
   - 上传并运行

4. **运行项目**
   - 选择运行到微信开发者工具
   - 或运行到浏览器进行 H5 调试

## 项目结构

```
MarkEraser/
├── pages/                          # 页面目录
│   ├── watermark-remover/          # 去水印主页面
│   ├── history/                    # 历史记录页面
│   ├── profile/                    # 用户中心页面
│   └── test/                       # 测试页面
├── uniCloud-aliyun/                # 云端代码
│   └── cloudfunctions/
│       └── watermark-remover/      # 去水印云函数
├── static/                         # 静态资源
├── pages.json                      # 页面配置
└── manifest.json                   # 应用配置
```

## 注意事项

### 版权声明
- 本工具仅供学习和研究使用
- 请尊重原创作者的版权
- 不得用于商业用途或侵权行为

---

### 原 uniCloud 示例项目说明

### 仓库分支与 HBuilder 版本对应关系

- main 对应 [HBuilder](https://www.dcloud.io/hbuilderx.html) 正式版
- alpha 对应 [HBuilder](https://www.dcloud.io/hbuilderx.html) Alpha 版
- dev 对应 [HBuilder](https://www.dcloud.io/hbuilderx.html) 内部 dev 版
