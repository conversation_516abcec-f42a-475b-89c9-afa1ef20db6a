# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

**MarkEraser** 是一个基于 uni-app 和 uniCloud 开发的视频去水印工具。支持解析抖音、快手、小红书等平台的分享链接，提取无水印的内容。

### 核心架构
- **前端**: uni-app + Vue 3，支持多端（微信小程序、H5、App）
- **后端**: uniCloud（阿里云版）+ 云函数处理内容
- **主要功能**: 链接解析、本地文件处理、历史记录、用户中心

## 核心组件

### 主要页面结构
- `pages/watermark-remover/index.vue` - 去水印主界面
- `pages/history/index.vue` - 历史记录页面
- `pages/profile/index.vue` - 用户中心和设置
- `pages/result/index.vue` - 处理结果展示
- `pages/test/` - 各平台测试页面

### 云函数 (`uniCloud-aliyun/cloudfunctions/`)
- `watermark-remover/` - 主要处理逻辑，支持链接和文件处理
- `xiaohongshu-parser/` - 小红书内容专用解析器
- `simple-douyin-parser/` - 抖音内容解析器
- `video-crawler/` - Python 视频提取工具（包含 requirements.txt）

### 配置文件
- `pages.json` - 页面路由和底部导航配置（3个主要标签：去水印、历史、我的）
- `manifest.json` - 应用配置，微信小程序 appid: wxb29e961aeb461493
- `App.vue` - 全局应用初始化和升级检查

## 开发流程

### 运行项目
```bash
# package.json 中没有定义 npm 脚本
# 开发通过 HBuilderX IDE 进行：
# 1. 在 HBuilderX 中打开项目
# 2. 配置 uniCloud（阿里云版本）
# 3. 上传云函数
# 4. 运行到微信开发者工具或 H5 浏览器
```

### 云函数开发
```bash
# 云函数位于 uniCloud-aliyun/cloudfunctions/
# 每个函数都有自己的 package.json 和依赖
# 通过 HBuilderX 部署：右键函数 -> 上传并运行
```

### 测试
- `pages/test/` 目录包含各平台解析器的测试页面
- Live Photo 测试：`pages/test/live-photo-test.vue`
- 水印测试：`pages/test/watermark-test.vue`

## 平台特性

### 支持的平台
- **抖音**: 视频链接解析和内容提取
- **小红书**: 图片和视频内容，支持 Live Photo
- **快手**: 视频内容解析
- **本地文件**: 图片和视频处理

### 云函数架构
- 每个平台都有专用的解析函数
- `watermark-remover/index.js` 中包含通用处理逻辑
- 全程调试日志和错误处理
- 支持链接处理和文件上传两种模式

## 重要说明

### uniCloud 集成
- 项目使用阿里云版本的 uniCloud
- 数据库 schema 位于 `uniCloud-aliyun/database/`
- 通过 uni-id 系统进行身份认证
- 微信小程序安全网络初始化

### 多端兼容性
- Vue 3 + uni-app 框架
- 支持微信小程序、H5 和原生 App
- 结果页面使用自定义导航样式
- 平台特定条件编译（#ifdef）

### 内容处理流程
1. 链接输入验证和解析
2. 平台检测（抖音、小红书等）
3. 云函数处理
4. 内容提取和去水印
5. 结果展示和下载选项
6. 历史记录保存（可选，用户控制）

## 开发环境要求
- 需要 HBuilderX 3.0+
- 微信开发者工具用于小程序测试
- uniCloud 控制台用于云函数管理
- Vue 3 + uni-app 框架