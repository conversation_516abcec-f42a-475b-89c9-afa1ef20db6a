# 图片预览交互优化说明

## 问题描述

之前的图片预览逻辑存在问题：
- 主图始终显示第一张图片，不会跟随用户选择变化
- 用户点击画廊中的图片时，主图没有响应
- 交互逻辑不够直观

## 优化方案

### 1. 主图响应式显示
- **动态主图**：主图现在会根据用户选择显示对应的图片
- **当前图片索引**：添加 `currentImageIndex` 状态跟踪当前选中的图片
- **图片计数器**：在主图底部显示 "X / Y" 格式的图片计数

### 2. 交互方式优化

#### 点击交互
- **点击画廊图片**：切换主图显示
- **点击Live Photo按钮**：播放对应的Live Photo视频
- **点击主图Live Photo按钮**：播放当前图片的Live Photo

#### 长按交互
- **长按画廊图片**：全屏预览图片
- **长按主图**：全屏预览当前图片

### 3. 视觉反馈

#### 选中状态指示
- **蓝色边框**：选中的画廊图片显示蓝色边框
- **选中标记**：选中图片左上角显示蓝色 "✓" 标记
- **缩放效果**：选中图片轻微缩放提供视觉反馈

#### Live Photo 标识
- **主图Live Photo按钮**：右上角显示 "LIVE PHOTO" 按钮
- **Live Photo指示器**：底部显示 "🎬 Live Photo" 标识
- **画廊Live Photo按钮**：右上角显示 "LIVE" 小按钮

## 技术实现

### 1. 数据结构
```javascript
data() {
  return {
    currentImageIndex: 0,  // 当前选中的图片索引
    // ... 其他数据
  }
}
```

### 2. 核心方法

#### getCurrentImageUrl()
```javascript
getCurrentImageUrl() {
  if (this.resultData.processedData?.imageUrls && this.resultData.processedData.imageUrls.length > 0) {
    return this.resultData.processedData.imageUrls[this.currentImageIndex] || this.resultData.processedData.imageUrls[0]
  }
  return this.resultData.processedData?.data || ''
}
```

#### selectImage(index)
```javascript
selectImage(index) {
  console.log('选择图片:', index + 1)
  this.currentImageIndex = index
  
  // 触觉反馈
  uni.vibrateShort({
    type: 'light'
  })
}
```

### 3. 界面组件

#### 主图容器
```vue
<view class="main-image-container">
  <image :src="getCurrentImageUrl()" />
  
  <!-- Live Photo 播放按钮 -->
  <view v-if="hasLivePhotoForImage(currentImageIndex)" 
        class="main-live-photo-btn" 
        @click="playLivePhotoForImage(currentImageIndex)">
    <view class="main-play-icon">▶</view>
    <text class="main-live-text">LIVE PHOTO</text>
  </view>
  
  <!-- 图片信息 -->
  <view class="main-image-info">
    <text class="image-counter">{{ currentImageIndex + 1 }} / {{ imageUrls.length }}</text>
    <text v-if="hasLivePhotoForImage(currentImageIndex)" class="live-indicator">🎬 Live Photo</text>
  </view>
</view>
```

#### 画廊图片
```vue
<view class="image-container" :class="{ 'active': currentImageIndex === index }">
  <image 
    :src="imageUrl"
    @click="selectImage(index)"
    @longpress="previewImage(imageUrl, imageUrls)"
  />
  
  <!-- Live Photo 按钮 -->
  <view v-if="hasLivePhotoForImage(index)" 
        class="live-photo-play-btn" 
        @click.stop="playLivePhotoForImage(index)">
    <view class="play-icon-small">▶</view>
    <text class="live-text">LIVE</text>
  </view>
  
  <!-- 选中指示器 -->
  <view v-if="currentImageIndex === index" class="selected-indicator">✓</view>
</view>
```

## 用户体验提升

### 1. 直观的交互
- **一键切换**：点击画廊图片立即切换主图
- **状态清晰**：当前选中的图片有明显的视觉标识
- **操作反馈**：每次选择都有轻微的触觉反馈

### 2. 多种预览方式
- **主图预览**：在当前页面查看大图
- **全屏预览**：长按进入系统全屏预览模式
- **Live Photo播放**：专用播放器播放动态内容

### 3. 信息丰富
- **图片计数**：清楚显示当前是第几张图片
- **Live Photo标识**：明确标识哪些图片有动态内容
- **选中状态**：清楚显示当前选中的图片

## 兼容性说明

- **向后兼容**：保留原有的 `previewImage` 方法用于全屏预览
- **渐进增强**：新功能不影响基础的图片显示功能
- **响应式设计**：适配不同屏幕尺寸和设备

---

*优化完成时间：2025-01-26*
*适用版本：MarkEraser v1.0*
