# Live Photo 下载功能优化说明

## 问题描述

之前的Live Photo下载功能存在以下问题：
1. **单独下载卡住**：点击单个Live Photo的保存按钮后，下载完第一个就卡住不动
2. **强制全部下载**：没有选择功能，必须一次性下载所有Live Photo
3. **文件较大**：Live Photo视频文件比较大，全部下载耗时长且占用存储空间
4. **用户体验差**：无法根据需要选择性保存

## 优化方案

### 1. 修复下载卡住问题
- **Promise包装**：使用Promise正确包装uni.downloadFile和uni.saveVideoToPhotosAlbum
- **错误处理**：完善的成功/失败回调处理
- **状态管理**：正确的异步状态管理

### 2. 添加Live Photo选择功能
- **可视化选择器**：创建专门的Live Photo选择组件
- **预览功能**：显示每个Live Photo的缩略图预览
- **多选支持**：支持选择多个或全部Live Photo
- **灵活操作**：支持全选、取消、单独选择等操作

### 3. 优化用户体验
- **清晰界面**：直观的选择界面，显示Live Photo编号和预览
- **进度反馈**：下载过程中显示进度和状态
- **智能提示**：根据选择数量显示相应提示

## 技术实现

### 1. 修复下载Promise问题

#### 原问题代码
```javascript
// 问题：没有正确处理Promise
const downloadTask = uni.downloadFile({
  url: videoUrl,
  success: (res) => {
    // 处理成功
  },
  fail: (error) => {
    // 处理失败
  }
})
```

#### 修复后代码
```javascript
// 解决：使用Promise包装
await new Promise((resolve, reject) => {
  const downloadTask = uni.downloadFile({
    url: videoUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveVideoToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => resolve(),
          fail: (error) => reject(error)
        })
      } else {
        reject(new Error(`下载失败，状态码: ${res.statusCode}`))
      }
    },
    fail: (error) => reject(error)
  })
})
```

### 2. Live Photo选择器组件

#### 组件特性
- **响应式设计**：适配不同屏幕尺寸
- **预览功能**：显示视频缩略图和播放图标
- **多选交互**：复选框选择，支持全选/取消
- **状态反馈**：显示已选择数量

#### 组件接口
```vue
<live-photo-selector
  :show="showLivePhotoSelector"
  :videoUrls="livePhotoSelectorVideos"
  :imageUrls="livePhotoSelectorImages"
  @confirm="onLivePhotoSelectorConfirm"
  @cancel="onLivePhotoSelectorCancel"
/>
```

#### 数据结构
```javascript
data() {
  return {
    showLivePhotoSelector: false,
    livePhotoSelectorVideos: [],
    livePhotoSelectorImages: [],
    currentLivePhotoSelectorResolve: null
  }
}
```

### 3. 选择流程优化

#### 保存选择流程
```javascript
async selectAndSaveLivePhotoVideos(videoUrls) {
  // 1. 显示选择界面
  const selectedIndexes = await this.showLivePhotoSelectorDialog(videoUrls)
  
  // 2. 处理用户选择
  if (!selectedIndexes || selectedIndexes.length === 0) {
    return // 用户取消或未选择
  }
  
  // 3. 获取选中的视频
  const selectedVideoUrls = selectedIndexes.map(index => videoUrls[index])
  const selectedVideoInfo = selectedIndexes.map(index => ({ url: videoUrls[index], index }))
  
  // 4. 保存选中的视频
  await this.saveLivePhotoVideos(selectedVideoUrls, selectedVideoInfo)
}
```

#### 选择器调用
```javascript
async showLivePhotoSelectorDialog(videoUrls) {
  return new Promise((resolve) => {
    if (videoUrls.length === 1) {
      resolve([0]) // 单个直接返回
      return
    }
    
    // 显示选择器
    this.livePhotoSelectorVideos = videoUrls
    this.livePhotoSelectorImages = this.resultData.processedData?.imageUrls || []
    this.showLivePhotoSelector = true
    this.currentLivePhotoSelectorResolve = resolve
  })
}
```

## 用户体验提升

### 1. 灵活的选择方式
- **单个保存**：点击Live Photo播放器中的保存按钮
- **批量选择**：通过选择器选择多个Live Photo
- **全部保存**：一键选择所有Live Photo

### 2. 清晰的视觉反馈
- **预览缩略图**：每个Live Photo显示对应的图片预览
- **选择状态**：复选框清晰显示选中状态
- **数量统计**：实时显示已选择的数量

### 3. 智能的交互设计
- **自适应处理**：单个Live Photo时直接保存，多个时显示选择器
- **取消机制**：支持取消选择，不强制保存
- **进度提示**：下载过程中显示进度和状态

## 性能优化

### 1. 下载优化
- **串行下载**：避免并发下载过多导致网络拥堵
- **错误恢复**：单个下载失败不影响其他文件
- **进度反馈**：实时显示下载进度

### 2. 内存管理
- **按需加载**：只加载选中的Live Photo
- **及时释放**：下载完成后及时释放临时文件
- **缓存控制**：合理控制预览图片的缓存

### 3. 用户体验
- **响应式界面**：选择器界面流畅响应
- **状态持久化**：保持用户的选择状态
- **错误处理**：友好的错误提示和处理

## 兼容性说明

- **向后兼容**：保留原有的单个下载功能
- **渐进增强**：新功能不影响基础下载功能
- **跨平台支持**：在不同平台上都有良好表现

## 使用说明

### 1. 单个Live Photo保存
- 在Live Photo播放器中点击"保存到相册"按钮
- 直接下载当前Live Photo到相册

### 2. 批量Live Photo保存
- 在主保存菜单中选择"保存Live Photo视频"
- 在选择器中选择要保存的Live Photo
- 点击"保存选中项"开始批量下载

### 3. 全部Live Photo保存
- 在选择器中点击"全选"按钮
- 或在主保存菜单中选择"保存全部"

---

*优化完成时间：2025-01-26*
*适用版本：MarkEraser v1.0*
