# 视频解析器开发经验总结

## 小红书解析修复经验

### 问题背景
小红书解析器遇到以下问题：
1. 标题只显示"小红书"，无法获取真实标题
2. 视频无法播放，提示 `MEDIA_ERR_SRC_NOT_SUPPORTED`
3. 作者信息抓取错误
4. 前端出现 `Refused to set unsafe header "User-Agent"` 错误

### 修复过程与关键点

#### 1. 前端User-Agent问题修复
**问题**：小程序环境不允许设置某些"不安全"的请求头
**解决方案**：
- 移除前端所有 `User-Agent` 设置
- 根据URL来源动态设置 `Referer`
- 对小红书页面链接跳过文件大小检查

```javascript
// 修复前
header: {
  'User-Agent': 'Mozilla/5.0...',
  'Referer': 'https://www.douyin.com/'
}

// 修复后
const headers = {}
if (videoUrl.includes('douyin') || videoUrl.includes('aweme')) {
  headers['Referer'] = 'https://www.douyin.com/'
} else if (videoUrl.includes('xiaohongshu')) {
  headers['Referer'] = 'https://www.xiaohongshu.com/'
}
```

#### 2. 视频URL提取优化
**关键改进**：
1. **URL解码处理**：`\u002F` → `/`
2. **过滤图片URL**：排除包含 `imageView`、`format/jpg` 的链接
3. **扩展匹配模式**：增加更多视频URL匹配规则

```javascript
// 关键的URL解码和过滤逻辑
if (match && match[1]) {
  let url = match[1];
  // 解码Unicode字符
  url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
  // 检查是否为视频URL（不是图片URL）
  if (url.includes('.mp4') || (url.includes('sns-') && !url.includes('imageView'))) {
    videoUrl = url;
    break;
  }
}
```

#### 3. 多层次解析策略
**策略层次**：
1. 页面HTML直接提取
2. 小红书API调用
3. 构造视频URL
4. 页面URL备用

```javascript
// 多层次解析流程
if (!videoUrl) {
  // 方法1：API获取
  try {
    const apiResult = await getXhsNoteDetail(noteId);
    if (apiResult && apiResult.videoUrl) {
      videoUrl = apiResult.videoUrl;
    }
  } catch (apiError) {
    // 方法2：构造URL
    try {
      const constructResult = await tryConstructVideoUrl(noteId, realUrl);
      if (constructResult && constructResult.videoUrl) {
        videoUrl = constructResult.videoUrl;
      }
    } catch (constructError) {
      // 方法3：页面URL备用
      videoUrl = realUrl;
    }
  }
}
```

#### 4. 作者信息提取优化
**问题**：抓取到错误的作者字段
**解决方案**：
- 优先查找用户信息中的昵称
- 过滤明显错误的作者名
- 按优先级匹配不同的JSON字段

```javascript
const authorPatterns = [
  // 优先级从高到低
  /"user":\s*{[^}]*"nickname":\s*"([^"]+)"/,
  /"user_info"[^}]*"nickname":\s*"([^"]+)"/,
  /"note_card"[^}]*"user"[^}]*"nickname":\s*"([^"]+)"/,
  /"nickname":\s*"([^"]+)"/
];
```

### 通用经验总结

#### 1. 调试策略
- **详细日志**：每个步骤都要有日志输出
- **内容分析**：统计关键词出现次数
- **数据结构探索**：输出JSON数据块的结构

#### 2. URL处理最佳实践
- **Unicode解码**：处理 `\u002F` 等编码
- **URL验证**：区分视频URL和图片URL
- **格式检查**：确保URL格式正确

#### 3. 错误处理
- **多层次备用方案**：API失败时有备用方法
- **环境适配**：区分小程序和浏览器环境
- **请求头优化**：避免被拒绝的请求头

#### 4. 数据提取技巧
- **优先级匹配**：按重要性排序匹配模式
- **数据过滤**：排除明显错误的数据
- **字段验证**：检查提取数据的有效性

## 其他平台对接指南

### 通用开发流程
1. **分析页面结构**：查看HTML和JSON数据
2. **识别关键字段**：标题、作者、视频URL
3. **设计匹配模式**：正则表达式或JSON解析
4. **实现多层次策略**：直接提取 → API调用 → 构造URL
5. **错误处理和备用方案**

### 常见问题和解决方案
1. **反爬虫机制**：使用合适的User-Agent和Referer
2. **动态内容**：查找JavaScript中的数据
3. **URL编码**：处理各种编码格式
4. **请求限制**：实现重试和备用方案

### 代码结构建议
```javascript
async function parseVideoContent(shareUrl) {
  try {
    // 1. 获取真实URL
    const realUrl = await getRealUrl(shareUrl);
    
    // 2. 获取页面内容
    const pageContent = await getPageContent(realUrl);
    
    // 3. 提取基本信息
    const basicInfo = await extractBasicInfo(pageContent);
    
    // 4. 多层次视频URL提取
    let videoUrl = await extractVideoUrl(pageContent);
    if (!videoUrl) {
      videoUrl = await tryAPIMethod(noteId);
    }
    if (!videoUrl) {
      videoUrl = await constructVideoUrl(noteId);
    }
    
    // 5. 构建返回结果
    return buildResult(basicInfo, videoUrl, realUrl);
    
  } catch (error) {
    console.error('解析失败:', error);
    throw error;
  }
}
```

### 测试和验证
- **多种链接测试**：不同类型的分享链接
- **错误场景测试**：网络失败、无效链接等
- **性能测试**：响应时间和成功率
- **兼容性测试**：不同环境下的表现

## 总结
成功修复小红书解析的关键在于：
1. **系统性分析**：从前端到后端全面排查
2. **多层次策略**：不依赖单一解析方法
3. **详细调试**：充分的日志和数据分析
4. **环境适配**：考虑不同运行环境的限制
5. **数据验证**：确保提取数据的准确性

这些经验可以应用到其他视频平台的解析开发中。
