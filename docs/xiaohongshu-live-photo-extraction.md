# 小红书 Live Photo 视频提取机制详解

## 概述

小红书的 Live Photo（实时照片）是一种特殊的媒体格式，类似于苹果 iPhone 的实时照片功能。它在静态图片的基础上包含一小段视频，为用户提供更丰富的视觉体验。

## Live Photo 数据结构

### JSON 数据格式

在小红书的页面 HTML 中，Live Photo 的数据通常嵌入在 JSON 结构中：

```json
{
  "image_list": [
    {
      "url": "https://ci.xiaohongshu.com/xxx.jpg",
      "url_size_large": "https://ci.xiaohongshu.com/xxx.jpg",
      "live_photo": {
        "media": {
          "stream": {
            "h264": [
              {
                "master_url": "https://sns-video-bd.xhscdn.com/xxx.mp4",
                "backup_urls": [
                  "https://sns-video-qc.xhscdn.com/xxx.mp4",
                  "https://sns-video-hw.xhscdn.com/xxx.mp4"
                ]
              }
            ]
          }
        }
      }
    }
  ]
}
```

### 关键字段说明

- `image_list`: 包含所有图片信息的数组
- `url` / `url_size_large`: 静态图片的 URL
- `live_photo.media.stream.h264`: Live Photo 的视频流信息
- `master_url`: 主要的视频 URL
- `backup_urls`: 备用视频 URL 数组

## 提取算法实现

### 1. 查找图片列表

```javascript
// 查找页面中的图片列表 JSON
const imageListPatterns = [
  /"image_list":\s*(\[[^\]]*\])/g,
  /"images":\s*(\[[^\]]*\])/g
];

for (const pattern of imageListPatterns) {
  let match;
  while ((match = pattern.exec(html)) !== null) {
    const imageListStr = match[1];
    // 解析 JSON 数据
    const imageList = JSON.parse(imageListStr);
    // 处理每个图片项...
  }
}
```

### 2. 提取静态图片和 Live Photo 视频

```javascript
if (Array.isArray(imageList)) {
  for (const imageItem of imageList) {
    if (typeof imageItem === 'object' && imageItem !== null) {
      
      // 1. 提取静态图片 URL
      const imageUrl = imageItem.url_size_large ||
                      imageItem.url_default ||
                      imageItem.url;
      
      if (imageUrl && typeof imageUrl === 'string') {
        const cleanImageUrl = imageUrl.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        imageUrls.push(cleanImageUrl);
        addDebugLog('提取到图片URL: ' + cleanImageUrl);
      }

      // 2. 提取 Live Photo 的视频 URL
      if (imageItem.live_photo && 
          imageItem.live_photo.media && 
          imageItem.live_photo.media.stream) {
        
        const livePhotoStream = imageItem.live_photo.media.stream;
        
        if (livePhotoStream.h264 && livePhotoStream.h264.length > 0) {
          // 优先使用 master_url，如果没有则使用第一个 backup_url
          const videoUrl = livePhotoStream.h264[0].master_url || 
                          livePhotoStream.h264[0].backup_urls?.[0];
          
          if (videoUrl && typeof videoUrl === 'string') {
            const cleanVideoUrl = videoUrl.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
            videoUrls.push(cleanVideoUrl);
            addDebugLog('提取到Live Photo视频URL: ' + cleanVideoUrl);
          }
        }
      }
    }
  }
}
```

### 3. URL 清理和解码

```javascript
// Unicode 字符解码
const cleanUrl = rawUrl.replace(/\\u002F/g, '/').replace(/\\\//g, '/');

// 示例转换：
// 原始: "https:\\u002F\\u002Fsns-video-bd.xhscdn.com\\u002Fxxx.mp4"
// 清理后: "https://sns-video-bd.xhscdn.com/xxx.mp4"
```

## 视频 URL 特征

### 域名模式

Live Photo 视频通常托管在以下 CDN 域名：

- `sns-video-bd.xhscdn.com` (百度云)
- `sns-video-qc.xhscdn.com` (腾讯云)
- `sns-video-hw.xhscdn.com` (华为云)
- `sns-video-al.xhscdn.com` (阿里云)

### URL 格式

```
https://sns-video-bd.xhscdn.com/stream/110/xxx/xxx.mp4
https://sns-video-qc.xhscdn.com/xxx_1080p.mp4
```

## 提取流程图

```mermaid
graph TD
    A[开始解析页面] --> B[查找 image_list JSON]
    B --> C[解析 JSON 数据]
    C --> D[遍历图片项]
    D --> E[提取静态图片 URL]
    E --> F[检查是否有 live_photo]
    F -->|有| G[提取 live_photo.media.stream.h264]
    F -->|无| H[继续下一张图片]
    G --> I[获取 master_url 或 backup_urls]
    I --> J[URL 解码和清理]
    J --> K[添加到视频 URL 列表]
    K --> H
    H --> L{还有图片?}
    L -->|是| D
    L -->|否| M[返回结果]
```

## 实际应用场景

### 1. 内容类型判断

```javascript
const hasImages = imageUrls.length > 0;
const hasVideos = videoUrls.length > 0; // 包含 Live Photo 视频

if (hasImages && hasVideos) {
  // 混合内容：既有静态图片又有 Live Photo 视频
  contentType = 'mixed';
} else if (hasImages) {
  // 纯图片内容
  contentType = 'image';
} else if (hasVideos) {
  // 纯视频内容
  contentType = 'video';
}
```

### 2. 播放器处理

```javascript
// 在我们的测试模式中，强制所有内容走视频逻辑
if (hasVideos) {
  // 有 Live Photo 视频，直接播放视频
  processedData = {
    data: videoUrl, // Live Photo 的视频 URL
    type: 'video/mp4',
    isUrl: true
  };
} else if (hasImages) {
  // 只有静态图片，强制当作视频处理
  processedData = {
    data: imageUrls[0], // 图片 URL
    type: 'video/mp4', // 强制标记为视频
    isUrl: true
  };
}
```

## 调试信息

在提取过程中，系统会输出详细的调试日志：

```
开始提取媒体URL（图片和视频）...
找到图片列表JSON: [{"url":"https://ci.xiaohongshu.com/xxx.jpg","live_photo":{"media":...
提取到图片URL: https://ci.xiaohongshu.com/xxx.jpg
提取到Live Photo视频URL: https://sns-video-bd.xhscdn.com/xxx.mp4
```

## 为什么图文内容能够播放

当你测试小红书图文链接时，能够播放的原因是：

1. **Live Photo 检测**：代码检测到图片项中包含 `live_photo` 字段
2. **视频 URL 提取**：从 `live_photo.media.stream.h264[0].master_url` 提取到真实的 MP4 视频链接
3. **强制视频处理**：由于我们修改了逻辑，所有内容都走视频处理流程
4. **播放器接收**：播放器接收到的是真实的视频 URL，而不是静态图片

### 实际播放内容

- **不是静态图片**：播放的不是 JPG/PNG 图片
- **是 Live Photo 视频**：播放的是 Live Photo 中包含的短视频片段
- **真实 MP4 格式**：URL 格式类似 `https://sns-video-bd.xhscdn.com/xxx.mp4`

## 🎯 重大发现：获取到无水印原图资源！

### 核心优势

通过对比测试发现了一个**重大优势**：

**对比测试结果：**
- **小红书官方App下载**：图片/视频带有水印
- **我们的API解析下载**：图片/视频无水印，为原始高质量内容

### 技术原理分析

#### 小红书的内容处理架构：
```
用户上传 → 原始内容存储(CDN) → 水印处理 → 展示版本(App)
              ↑                           ↑
          我们获取的URL               官方App下载版本
```

#### 我们的优势来源：
1. **直接访问CDN原图**：通过API解析获取到原始存储地址
2. **绕过展示层处理**：避开了水印添加的展示层
3. **原始资源访问**：获得了"内部级别"的资源访问权限

### 🚀 产品核心竞争力

#### 用户价值：
- **原图质量**：无水印、无压缩的原始内容
- **真实还原**：保持创作者的原始作品完整性
- **高效获取**：无需复杂的去水印后处理

#### 技术优势：
- **API级别访问**：获得比普通用户更高的访问权限
- **原始资源直达**：直接访问CDN存储的原始文件
- **无损内容获取**：避免了展示层的质量损失

#### 商业价值：
- **差异化竞争**：提供比官方更好的下载体验
- **用户粘性**：高质量内容获取成为核心卖点
- **技术壁垒**：API解析技术形成竞争护城河

## 总结

Live Photo 视频提取的核心在于：

1. **识别数据结构**：在 `image_list` 中查找 `live_photo` 字段
2. **层级访问**：按照 `live_photo.media.stream.h264` 的路径访问视频数据
3. **URL 优选**：优先使用 `master_url`，备选 `backup_urls`
4. **格式清理**：解码 Unicode 字符，确保 URL 格式正确
5. **天然优势**：提取的视频天然无水印，质量更高

这种机制使得看似"图文"的内容实际上可以播放动态视频，为用户提供了更丰富的交互体验，同时意外获得了无水印的高质量内容。

---

*文档创建时间：2025-01-26*
*适用版本：小红书解析器 v1.0*
