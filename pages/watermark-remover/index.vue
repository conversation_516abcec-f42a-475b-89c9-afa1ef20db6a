<template>
  <view class="container">
    <!-- 顶部标题区域 -->
    <view class="header">
      <text class="title">视频去水印</text>
      <text class="subtitle">支持抖音、快手等平台链接解析</text>
    </view>

    <!-- 输入区域 -->
    <view class="input-section">
      <view class="input-header">
        <text class="input-title">🔗 粘贴分享链接</text>
        <text class="clear-btn" @click="clearInput" v-if="linkInput">清空</text>
      </view>
      
      <view class="input-container">
        <textarea
          v-model="linkInput"
          placeholder="直接粘贴，支持视频分享链接，例如：3.38 复制打开抖音，看看【coral的图文作品】..."
          class="link-input"
          :maxlength="500"
          auto-height
        />
      </view>
      
      <!-- 功能开关 -->
      <view class="switches">
        <view class="switch-item">
          <text class="switch-label">记录历史</text>
          <switch :checked="saveHistory" @change="onSaveHistoryChange" color="#007AFF" />
        </view>
        <view class="switch-item">
          <text class="switch-label">自动粘贴</text>
          <switch :checked="autoPaste" @change="onAutoPasteChange" color="#007AFF" />
        </view>

      </view>
    </view>

    <!-- 移除了分割线和文件选择区域 -->

    <!-- 提取按钮 -->
    <view class="action-section">
      <button
        class="extract-btn"
        :class="{ 'disabled': !canExtract }"
        :disabled="!canExtract"
        @click="extractContent"
        :loading="isLoading"
      >
        {{ isLoading ? '正在处理...' : '提取无水印内容' }}
      </button>


      
      <view class="tips">
        <text class="tips-text">ℹ️ 版权归平台及作者所有，本工具不储存任何内容</text>
      </view>

      <!-- 功能说明 -->
      <view class="feature-info">
        <text class="feature-title">💡 功能说明</text>
        <view class="feature-list">
          <text class="feature-item">✅ 支持抖音、快手、小红书等平台</text>
          <text class="feature-item">✅ 智能解析获取高清无水印视频</text>
          <text class="feature-item">✅ 一键保存到相册，方便分享</text>
          <text class="feature-item">🚀 持续优化中，支持更多平台</text>
        </view>
      </view>

      <!-- 免责声明 -->
      <view class="disclaimer">
        <text class="disclaimer-title">⚠️ 免责声明</text>
        <text class="disclaimer-text">本工具仅供个人学习交流使用，禁止用于任何商业或侵权用途。若因用户行为引发纠纷，本平台不承担任何责任。</text>
      </view>
    </view>

    <!-- 移除处理结果区域，用户已经在结果页面查看过了 -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      linkInput: '',
      saveHistory: true,
      autoPaste: false,
      isLoading: false,
    }
  },
  
  computed: {
    canExtract() {
      return this.linkInput.trim() && !this.isLoading
    }
  },
  
  onLoad() {
    this.loadSettings()
    if (this.autoPaste) {
      this.autoGetClipboard()
    }

    // 监听历史页面的重新处理事件
    uni.$on('reprocessItem', (item) => {
      if (item.originalLink) {
        this.linkInput = item.originalLink
      }
    })
  },

  onShow() {
    if (this.autoPaste) {
      this.autoGetClipboard()
    }
  },

  onUnload() {
    // 移除事件监听
    uni.$off('reprocessItem')
  },
  
  methods: {
    // 清空输入
    clearInput() {
      this.linkInput = ''
    },
    
    // 保存历史开关
    onSaveHistoryChange(e) {
      this.saveHistory = e.detail.value
      this.saveSettings()
    },
    
    // 自动粘贴开关
    onAutoPasteChange(e) {
      this.autoPaste = e.detail.value
      this.saveSettings()
      if (this.autoPaste) {
        this.autoGetClipboard()
      }
    },


    
    // 自动获取剪贴板
    autoGetClipboard() {
      uni.getClipboardData({
        success: (res) => {
          const data = res.data
          if (data && this.isValidLink(data) && data !== this.linkInput) {
            this.linkInput = data
          }
        }
      })
    },
    
    // 验证链接有效性
    isValidLink(text) {
      if (!text || typeof text !== 'string') {
        return false
      }

      const patterns = [
        /douyin\.com/i,
        /dy\.com/i,
        /kuaishou\.com/i,
        /ks\.com/i,
        /xiaohongshu\.com/i,
        /xhslink\.com/i
      ]
      return patterns.some(pattern => pattern.test(text))
    },
    
    // 显示图片工具推荐
    showImageTools() {
      uni.showModal({
        title: '图片去水印工具推荐',
        content: `推荐使用以下专业工具：

1. cleanup.pictures
   - 完全免费的AI去水印工具
   - 网址：cleanup.pictures

2. remove.bg
   - 专业的背景移除工具
   - 网址：remove.bg

3. Adobe Photoshop
   - 使用"内容感知填充"功能
   - 专业级图像处理软件

4. GIMP (免费)
   - 开源图像编辑软件
   - 使用修复工具去除水印`,
        showCancel: true,
        cancelText: '知道了',
        confirmText: '复制链接',
        success: (res) => {
          if (res.confirm) {
            uni.setClipboardData({
              data: 'cleanup.pictures\nremove.bg',
              success: () => {
                uni.showToast({
                  title: '链接已复制',
                  icon: 'success'
                })
              }
            })
          }
        }
      })
    },
    
    // 提取内容
    async extractContent() {
      if (!this.canExtract) return

      console.log('开始提取内容...')
      this.isLoading = true

      try {
        let result
        if (this.linkInput.trim()) {
          console.log('处理链接:', this.linkInput.trim())
          // 处理链接
          result = await this.processLink(this.linkInput.trim())
        }

        console.log('处理结果:', result)

        // 保存历史记录
        if (this.saveHistory && result) {
          this.saveToHistory(result)
        }

        // 立即跳转到结果页面（不等待其他操作）
        uni.setStorageSync('temp_result', JSON.stringify(result))
        uni.navigateTo({
          url: '/pages/result/index',
          animationType: 'slide-in-right',
          animationDuration: 200 // 加快动画速度
        })

      } catch (error) {
        console.error('处理失败:', error)

        // 显示解析失败的详细提示
        uni.showModal({
          title: '解析失败',
          content: '解析失败，可能是因为：1、作品(或账号)设置成了私密 2、作品还未审核通过 3、作品已经被删除了',
          showCancel: false,
          confirmText: '确定'
        })
      } finally {
        this.isLoading = false
      }
    },
    
    // 处理链接
    async processLink(link) {
      // 清理链接
      const cleanedLink = this.cleanLink(link)
      console.log('清理后的链接:', cleanedLink)

      try {
        // 检查是否为有效链接
        if (!this.isValidLink(cleanedLink)) {
          throw new Error('不支持的链接格式，请检查链接是否正确')
        }

        console.log('调用云函数...')

        // 检测平台类型
        const platform = this.detectPlatform(cleanedLink)
        console.log('检测到平台:', platform)

        let result
        if (platform === 'xiaohongshu') {
          // 调用小红书解析器
          result = await uniCloud.callFunction({
            name: 'xiaohongshu-parser',
            data: {
              link: cleanedLink,
              forceRemoveWatermark: false, // 暂时不去水印，先获取原始内容
              debug: true // 开启调试以便查看详细信息
            }
          })
        } else {
          // 调用抖音解析器（默认）
          result = await uniCloud.callFunction({
            name: 'simple-douyin-parser',
            data: {
              link: cleanedLink,
              forceRemoveWatermark: true, // 强制使用去水印功能
              debug: false // 调试完成
            }
          })
        }

        console.log('云函数返回结果:', result)
        console.log('result.result:', result.result)
        console.log('result.result.success:', result.result?.success)
        console.log('result.result.data:', result.result?.data)

        // 输出调试日志
        if (result.result && result.result.debugLogs) {
          console.log('=== 云函数调试日志 ===')
          result.result.debugLogs.forEach(log => {
            console.log(log)
          })
          console.log('=== 调试日志结束 ===')
        }

        if (result.result && result.result.success) {
          const data = result.result.data
          // 保存原始链接用于重新处理
          data.originalLink = link
          console.log('解析成功，返回数据:', data)
          return data
        } else {
          const errorMsg = result.result?.message || '解析失败'
          console.error('云函数返回错误:', errorMsg)
          throw new Error(errorMsg)
        }
      } catch (error) {
        console.error('云函数调用失败:', error)

        // 如果是云函数相关错误，提供模拟数据
        if (error.message && (error.message.includes('FunctionNotFound') ||
            error.message.includes('uniCloud') ||
            error.message.includes('云函数'))) {
          console.log('云函数未部署，返回模拟数据')
          return this.getMockData(cleanedLink)
        }

        // 其他错误直接抛出
        throw new Error(error.message || '网络请求失败，请检查网络连接')
      }
    },

    // 获取模拟数据（用于测试）
    getMockData(link) {
      const platform = this.detectPlatform(link)

      // 模拟处理后的数据（base64格式）
      const mockVideoData = 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDE='
      const mockImageData = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='

      // 模拟不同平台的数据
      const mockData = {
        douyin: {
          title: '春梅饭馆活动来了，菜价本来都不贵，还有59抵100',
          author: '小新吃不饱',
          processedData: {
            data: mockVideoData,
            type: 'video/mp4',
            size: 1024000
          },
          type: 'video',
          platform: 'douyin',
          source: '抖音',
          originalLink: link,
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        },
        kuaishou: {
          title: '快手测试视频',
          author: '快手用户',
          processedData: {
            data: mockVideoData,
            type: 'video/mp4',
            size: 1024000
          },
          type: 'video',
          platform: 'kuaishou',
          source: '快手',
          originalLink: link,
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        },
        xiaohongshu: {
          title: '小红书美妆分享｜超好用的平价护肤品推荐',
          author: '美妆达人小仙女',
          processedData: [mockImageData, mockImageData, mockImageData], // 多张图片
          type: 'image',
          contentType: 'image',
          platform: 'xiaohongshu',
          source: '小红书',
          originalLink: link,
          isImageContent: true,
          imageUrls: [
            'https://example.com/image1.jpg',
            'https://example.com/image2.jpg',
            'https://example.com/image3.jpg'
          ],
          note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
        }
      }

      return mockData[platform] || {
        title: '未知平台内容',
        author: '未知作者',
        processedData: {
          data: mockImageData,
          type: 'image/jpeg',
          size: 512000
        },
        type: 'image',
        platform: 'unknown',
        source: '未知平台',
        originalLink: link,
        note: '这是模拟数据，真实功能需要部署云函数和爬虫逻辑'
      }
    },

    // 检测平台类型
    detectPlatform(link) {
      if (/douyin\.com|dy\.com/i.test(link)) {
        return 'douyin'
      } else if (/kuaishou\.com|ks\.com/i.test(link)) {
        return 'kuaishou'
      } else if (/xiaohongshu\.com|xhslink\.com/i.test(link)) {
        return 'xiaohongshu'
      }
      return null
    },

    // 获取平台名称
    getPlatformName(platform) {
      const names = {
        'douyin': '抖音',
        'kuaishou': '快手',
        'xiaohongshu': '小红书'
      }
      return names[platform] || '未知平台'
    },

    // 清理链接
    cleanLink(link) {
      // 移除多余的空格和换行
      link = link.trim().replace(/\s+/g, ' ')

      // 提取URL部分
      const urlMatch = link.match(/(https?:\/\/[^\s]+)/)
      if (urlMatch) {
        return urlMatch[1]
      }

      // 如果没有找到完整URL，尝试提取域名相关部分
      const domainMatch = link.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/)
      if (domainMatch) {
        return 'https://' + domainMatch[1]
      }

      return link
    },
    
    // 移除了文件处理相关方法
    
    // 移除获取媒体源的方法，这些功能现在在结果页面

    // 移除保存相关方法，这些功能现在在结果页面



    // 移除所有保存和分享相关方法，这些功能现在在结果页面


    
    // 保存到历史记录
    saveToHistory(data) {
      const history = uni.getStorageSync('watermark_history') || []
      const item = {
        ...data,
        timestamp: Date.now(),
        id: Date.now().toString()
      }
      history.unshift(item)
      
      // 只保留最近50条记录
      if (history.length > 50) {
        history.splice(50)
      }
      
      uni.setStorageSync('watermark_history', history)
    },
    
    // 加载设置
    loadSettings() {
      const settings = uni.getStorageSync('watermark_settings') || {}
      this.saveHistory = settings.saveHistory !== false
      this.autoPaste = settings.autoPaste === true
    },

    // 保存设置
    saveSettings() {
      const settings = {
        saveHistory: this.saveHistory,
        autoPaste: this.autoPaste
      }
      uni.setStorageSync('watermark_settings', settings)
    },

    // 视频加载开始
    onVideoLoadStart() {
      this.videoStatus = '正在加载视频...'
      console.log('视频开始加载')
    },

    // 视频可以播放
    onVideoCanPlay() {
      this.videoStatus = '视频加载完成'
      console.log('视频可以播放')
      setTimeout(() => {
        this.videoStatus = ''
      }, 2000)
    },

    // 视频加载错误
    onVideoError(e) {
      console.error('视频加载失败:', e)
      this.videoStatus = '视频加载失败，可能是网络问题或视频格式不支持'

      // 显示错误提示
      uni.showModal({
        title: '视频播放失败',
        content: '视频无法播放，可能原因：\n1. 网络连接问题\n2. 视频格式不支持\n3. 文件过大\n\n建议：尝试保存到相册后播放',
        showCancel: false,
        confirmText: '我知道了'
      })
    },

    // 获取数据大小
    getDataSize(processedData) {
      if (!processedData || !processedData.data) {
        return '未知'
      }

      if (processedData.isUrl) {
        return 'URL格式'
      }

      // 计算base64数据大小
      const base64Data = processedData.data.replace(/^data:[^;]+;base64,/, '')
      const sizeInBytes = (base64Data.length * 3) / 4

      if (sizeInBytes < 1024) {
        return `${Math.round(sizeInBytes)} B`
      } else if (sizeInBytes < 1024 * 1024) {
        return `${Math.round(sizeInBytes / 1024)} KB`
      } else {
        return `${Math.round(sizeInBytes / (1024 * 1024))} MB`
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

.header {
  text-align: center;
  padding: 60rpx 0 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  display: block;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.input-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.input-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.input-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-left: 16rpx;
  flex: 1;
}

.clear-btn {
  font-size: 28rpx;
  color: #007AFF;
}

.input-container {
  margin-bottom: 40rpx;
}

.link-input {
  width: 100%;
  min-height: 180rpx;
  padding: 24rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #F8F9FA;
  box-sizing: border-box;
}

.switches {
  display: flex;
  justify-content: space-between;
}

.switch-item {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 30rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.file-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.file-options {
  display: flex;
  justify-content: space-around;
}

.file-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  border: 2rpx dashed #E5E5E5;
  border-radius: 16rpx;
  width: 280rpx;
}

.file-option.disabled {
  opacity: 0.6;
  border-color: #F0F0F0;
  background: #FAFAFA;
}

.option-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.option-note {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.image-watermark-info {
  background: #F8F9FA;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  border: 2rpx solid #E9ECEF;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16rpx;
  display: block;
}

.info-content {
  margin-top: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: #6C757D;
  margin-bottom: 12rpx;
  display: block;
}

.info-link {
  font-size: 24rpx;
  color: #007AFF;
  margin-bottom: 8rpx;
  display: block;
  text-decoration: underline;
}

.action-section {
  padding: 0 20rpx;
}

.extract-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(45deg, #007AFF, #5AC8FA);
  color: #ffffff;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.extract-btn.disabled {
  background: #CCCCCC;
  box-shadow: none;
}

.extract-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.tips {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8rpx;
}

/* 移除处理结果相关的CSS样式 */

.feature-info {
  background: #F0F8FF;
  border: 1rpx solid #B3D9FF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #007AFF;
  display: block;
  margin-bottom: 20rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
}

.feature-item {
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.disclaimer {
  background: #FFF2F2;
  border: 1rpx solid #FFB3B3;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
}

.disclaimer-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #D32F2F;
  display: block;
  margin-bottom: 15rpx;
}

.disclaimer-text {
  font-size: 24rpx;
  color: #D32F2F;
  line-height: 1.6;
}

.processed-content {
  margin-top: 20rpx;
}

.processed-video {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  background: #F5F5F5;
}

.video-status {
  margin-top: 20rpx;
  text-align: center;
}

.status-text {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background: #F0F0F0;
  border-radius: 20rpx;
}

.processed-image {
  width: 100%;
  max-height: 600rpx;
  border-radius: 12rpx;
}

.result-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  padding: 0 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}

.save-btn {
  background: #34C759;
  color: #ffffff;
}

.share-btn {
  background: #007AFF;
  color: #ffffff;
}

.debug-info {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 8rpx;
  font-family: monospace;
}
</style>
