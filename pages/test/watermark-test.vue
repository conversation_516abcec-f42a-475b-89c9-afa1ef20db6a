<template>
  <view class="container">
    <view class="header">
      <text class="title">功能测试页面</text>
    </view>
    
    <view class="test-section">
      <view class="test-item">
        <text class="test-title">1. 测试抖音链接解析</text>
        <button @click="testDouyinLink" class="test-btn">测试抖音</button>
      </view>
      
      <view class="test-item">
        <text class="test-title">2. 测试快手链接解析</text>
        <button @click="testKuaishouLink" class="test-btn">测试快手</button>
      </view>

      <view class="test-item">
        <text class="test-title">3. 测试小红书链接解析</text>
        <button @click="testXiaohongshuLink" class="test-btn">测试小红书</button>
      </view>

      <view class="test-item">
        <text class="test-title">4. 测试本地存储</text>
        <button @click="testLocalStorage" class="test-btn">测试存储</button>
      </view>
      
      <view class="test-item">
        <text class="test-title">5. 测试云函数连接</text>
        <button @click="testCloudFunction" class="test-btn">测试云函数</button>
      </view>
    </view>
    
    <view class="result-section" v-if="testResult">
      <text class="result-title">测试结果：</text>
      <text class="result-content">{{ testResult }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testResult: ''
    }
  },
  
  methods: {
    // 测试抖音链接
    async testDouyinLink() {
      this.testResult = '正在测试抖音链接解析...'
      
      try {
        const result = await uniCloud.callFunction({
          name: 'watermark-remover',
          data: {
            type: 'link',
            content: 'https://v.douyin.com/OkoQMjjhxdI/'
          }
        })
        
        this.testResult = `抖音测试成功: ${JSON.stringify(result.result, null, 2)}`
      } catch (error) {
        this.testResult = `抖音测试失败: ${error.message}`
      }
    },
    
    // 测试快手链接
    async testKuaishouLink() {
      this.testResult = '正在测试快手链接解析...'
      
      try {
        const result = await uniCloud.callFunction({
          name: 'watermark-remover',
          data: {
            type: 'link',
            content: 'https://v.kuaishou.com/test123'
          }
        })
        
        this.testResult = `快手测试成功: ${JSON.stringify(result.result, null, 2)}`
      } catch (error) {
        this.testResult = `快手测试失败: ${error.message}`
      }
    },

    // 测试小红书链接
    async testXiaohongshuLink() {
      this.testResult = '正在测试小红书链接解析...'

      try {
        const result = await uniCloud.callFunction({
          name: 'xiaohongshu-parser',
          data: {
            link: 'https://www.xiaohongshu.com/explore/test123',
            forceRemoveWatermark: true,
            debug: true
          }
        })

        this.testResult = `小红书测试成功: ${JSON.stringify(result.result, null, 2)}`
      } catch (error) {
        this.testResult = `小红书测试失败: ${error.message}`
        console.error('小红书测试详细错误:', error)
      }
    },

    // 测试本地存储
    testLocalStorage() {
      try {
        // 测试存储
        const testData = {
          id: Date.now().toString(),
          title: '测试数据',
          timestamp: Date.now()
        }
        
        uni.setStorageSync('test_data', testData)
        const retrieved = uni.getStorageSync('test_data')
        
        if (retrieved && retrieved.id === testData.id) {
          this.testResult = '本地存储测试成功'
        } else {
          this.testResult = '本地存储测试失败'
        }
        
        // 清理测试数据
        uni.removeStorageSync('test_data')
      } catch (error) {
        this.testResult = `本地存储测试失败: ${error.message}`
      }
    },
    
    // 测试云函数连接
    async testCloudFunction() {
      this.testResult = '正在测试云函数连接...'
      
      try {
        const result = await uniCloud.callFunction({
          name: 'watermark-remover',
          data: {
            type: 'test',
            content: 'connection test'
          }
        })
        
        this.testResult = `云函数连接测试: ${JSON.stringify(result, null, 2)}`
      } catch (error) {
        this.testResult = `云函数连接失败: ${error.message}`
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: #F5F5F5;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.test-item:last-child {
  border-bottom: none;
}

.test-title {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.test-btn {
  background: #007AFF;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

.result-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.result-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
}
</style>
