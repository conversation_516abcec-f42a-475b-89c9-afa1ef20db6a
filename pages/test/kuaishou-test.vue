<template>
  <view class="container">
    <view class="header">
      <text class="title">快手解析测试</text>
    </view>
    
    <view class="test-section">
      <view class="input-section">
        <text class="label">测试链接：</text>
        <textarea 
          v-model="testLink" 
          class="link-input" 
          placeholder="请输入快手链接"
        ></textarea>
        <button @click="testParse" class="test-btn" :disabled="!testLink.trim()">
          开始测试
        </button>
      </view>
      
      <view class="preset-links">
        <text class="preset-title">预设测试链接：</text>
        <button @click="usePresetLink" class="preset-btn">
          使用示例链接
        </button>
      </view>
    </view>
    
    <view class="result-section" v-if="testResult">
      <text class="result-title">测试结果：</text>
      <view class="result-content">
        <text class="result-text">{{ testResult }}</text>
      </view>
    </view>
    
    <view class="log-section" v-if="logs.length > 0">
      <text class="log-title">调试日志：</text>
      <view class="log-content">
        <text 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-item"
        >{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      testLink: '',
      testResult: '',
      logs: []
    }
  },
  
  methods: {
    // 使用预设链接
    usePresetLink() {
      // 使用快手官方格式的测试链接
      this.testLink = 'https://www.kuaishou.com/short-video/3xiqjreqjdg3ka8'
    },
    
    // 测试解析
    async testParse() {
      if (!this.testLink.trim()) {
        uni.showToast({
          title: '请输入测试链接',
          icon: 'none'
        })
        return
      }
      
      this.testResult = '正在测试...'
      this.logs = []
      
      try {
        this.addLog('开始测试快手解析')
        this.addLog('原始链接: ' + this.testLink)
        
        // 清理链接
        const cleanedLink = this.cleanLink(this.testLink)
        this.addLog('清理后链接: ' + cleanedLink)
        
        // 检测平台
        const platform = this.detectPlatform(cleanedLink)
        this.addLog('检测到平台: ' + platform)
        
        if (platform !== 'kuaishou') {
          this.testResult = '错误：不是快手链接'
          return
        }
        
        this.addLog('调用快手解析云函数...')
        
        // 调用云函数
        const result = await uniCloud.callFunction({
          name: 'simple-kuaishou-parser',
          data: {
            link: cleanedLink,
            forceRemoveWatermark: true, // 强制去水印
            debug: true
          }
        })
        
        this.addLog('云函数调用完成')
        this.addLog('返回结果: ' + JSON.stringify(result.result, null, 2))

        // 显示云函数的调试日志
        if (result.result && result.result.debugLogs) {
          this.addLog('=== 云函数调试日志 ===')
          result.result.debugLogs.forEach(log => {
            this.addLog(log)
          })
          this.addLog('=== 调试日志结束 ===')
        }

        if (result.result && result.result.success) {
          this.testResult = '✅ 测试成功！\n' + JSON.stringify(result.result.data, null, 2)
        } else {
          this.testResult = '❌ 测试失败：' + (result.result?.message || '未知错误')
        }
        
      } catch (error) {
        this.addLog('测试出错: ' + error.message)
        this.testResult = '❌ 测试异常：' + error.message
        console.error('测试详细错误:', error)
      }
    },
    
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.push(`[${timestamp}] ${message}`)
    },
    
    // 清理链接
    cleanLink(link) {
      // 移除多余的空格和换行
      link = link.trim().replace(/\s+/g, ' ')

      // 提取URL部分
      const urlMatch = link.match(/(https?:\/\/[^\s]+)/)
      if (urlMatch) {
        return urlMatch[1]
      }

      // 如果没有找到完整URL，尝试提取域名相关部分
      const domainMatch = link.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/)
      if (domainMatch) {
        return 'https://' + domainMatch[1]
      }

      return link
    },
    
    // 检测平台类型
    detectPlatform(link) {
      if (/douyin\.com|dy\.com/i.test(link)) {
        return 'douyin'
      } else if (/kuaishou\.com|ks\.com/i.test(link)) {
        return 'kuaishou'
      } else if (/xiaohongshu\.com|xhslink\.com/i.test(link)) {
        return 'xiaohongshou'
      }
      return null
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.input-section {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.link-input {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background-color: #ff6600;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
}

.test-btn[disabled] {
  background-color: #ccc;
}

.preset-links {
  margin-top: 30rpx;
}

.preset-title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.preset-btn {
  width: 100%;
  height: 70rpx;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.result-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.result-content {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 10rpx;
  max-height: 300rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 24rpx;
  color: #666;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.log-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.log-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.log-content {
  padding: 20rpx;
  background-color: #000;
  border-radius: 10rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item {
  display: block;
  font-size: 20rpx;
  color: #0f0;
  font-family: monospace;
  line-height: 1.5;
  margin-bottom: 5rpx;
}
</style>